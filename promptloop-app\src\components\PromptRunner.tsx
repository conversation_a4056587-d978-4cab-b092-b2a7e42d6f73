import React, { useState, useEffect } from 'react';
import { Play, Square, Copy, Download, Clock, DollarSign, Zap, AlertCircle } from 'lucide-react';
import { Button } from './ui/Button';
import { Textarea } from './ui/Textarea';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';

interface ModelConfig {
  id: string;
  name: string;
  provider: string;
  status: 'connected' | 'disconnected' | 'testing' | 'error';
}

interface ModelResponse {
  modelId: string;
  response: string;
  latency: number;
  cost: number;
  status: 'success' | 'error' | 'running';
  error?: string;
  timestamp: Date;
}

export const PromptRunner: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [availableModels, setAvailableModels] = useState<ModelConfig[]>([]);
  const [responses, setResponses] = useState<ModelResponse[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    // Load available models from settings
    const loadModels = () => {
      try {
        const saved = localStorage.getItem('promptloop_models');
        if (saved) {
          const models = JSON.parse(saved);
          const connectedModels = models.filter((m: ModelConfig) => m.status === 'connected');
          setAvailableModels(connectedModels);
        }
      } catch (error) {
        console.error('Error loading models:', error);
      }
    };

    loadModels();
  }, []);

  const toggleModelSelection = (modelId: string) => {
    setSelectedModels(prev => 
      prev.includes(modelId) 
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  const mockApiCall = async (modelId: string, prompt: string): Promise<ModelResponse> => {
    const model = availableModels.find(m => m.id === modelId);
    const startTime = Date.now();
    
    // Simulate API call delay
    const delay = Math.random() * 3000 + 1000; // 1-4 seconds
    await new Promise(resolve => setTimeout(resolve, delay));
    
    const latency = Date.now() - startTime;
    
    // Simulate occasional errors
    if (Math.random() < 0.1) {
      return {
        modelId,
        response: '',
        latency,
        cost: 0,
        status: 'error',
        error: 'API rate limit exceeded',
        timestamp: new Date()
      };
    }

    // Generate mock responses based on model type
    let mockResponse = '';
    const cost = Math.random() * 0.05 + 0.001; // $0.001 - $0.051

    if (modelId.includes('gpt')) {
      mockResponse = `**GPT Response:**

Based on your prompt, here's a comprehensive response:

${prompt.length > 100 ? 'This is a detailed analysis of your complex prompt. ' : 'Here\'s a concise response to your query. '}

Key points:
• Structured approach to the problem
• Practical implementation steps  
• Considerations for best practices
• Potential challenges and solutions

The response demonstrates GPT's strength in providing structured, detailed answers with clear formatting and actionable insights.

*Generated by ${model?.name || 'GPT Model'}*`;
    } else if (modelId.includes('claude')) {
      mockResponse = `I'd be happy to help with your request.

${prompt.length > 100 ? 'Looking at your detailed prompt, I can provide a thorough analysis:' : 'For your query, here\'s my response:'}

My approach would be:

1. **Understanding the context**: ${prompt.substring(0, 50)}...
2. **Key considerations**: Safety, accuracy, and helpfulness
3. **Recommended solution**: A balanced approach that addresses your needs

I aim to be helpful while being thoughtful about potential implications. Is there any specific aspect you'd like me to elaborate on?

*Response from ${model?.name || 'Claude'}*`;
    } else if (modelId.includes('gemini')) {
      mockResponse = `## Gemini Analysis

**Query Processing**: ✅ Complete
**Safety Check**: ✅ Passed
**Response Generation**: ✅ Ready

### Response:

${prompt.length > 100 ? 'Your comprehensive prompt requires a multi-faceted response:' : 'Quick response to your query:'}

🔍 **Analysis**: The prompt shows clear intent and structure
⚡ **Solution**: Here's my recommended approach
🎯 **Outcome**: Expected results and next steps

### Technical Details:
- Processing time: ${latency}ms
- Confidence score: ${Math.floor(Math.random() * 20 + 80)}%
- Safety rating: High

*Powered by ${model?.name || 'Gemini'}*`;
    } else {
      mockResponse = `Response from ${model?.name || 'AI Model'}:

${prompt.length > 100 ? 'Analyzing your detailed prompt...' : 'Processing your query...'}

Here's my response based on the input provided. The model has processed your request and generated this output according to its training and capabilities.

Key insights:
- Relevant information extracted from prompt
- Contextual understanding applied
- Response tailored to your needs

This demonstrates the model's ability to understand and respond to various types of prompts effectively.`;
    }

    return {
      modelId,
      response: mockResponse,
      latency,
      cost,
      status: 'success',
      timestamp: new Date()
    };
  };

  const runPrompt = async () => {
    if (!prompt.trim() || selectedModels.length === 0) return;

    setIsRunning(true);
    setResponses([]);

    // Initialize responses with running status
    const initialResponses = selectedModels.map(modelId => ({
      modelId,
      response: '',
      latency: 0,
      cost: 0,
      status: 'running' as const,
      timestamp: new Date()
    }));
    setResponses(initialResponses);

    // Run all models concurrently
    const promises = selectedModels.map(async (modelId) => {
      try {
        const response = await mockApiCall(modelId, prompt);
        setResponses(prev => prev.map(r => 
          r.modelId === modelId ? response : r
        ));
        return response;
      } catch (error) {
        const errorResponse: ModelResponse = {
          modelId,
          response: '',
          latency: 0,
          cost: 0,
          status: 'error',
          error: 'Request failed',
          timestamp: new Date()
        };
        setResponses(prev => prev.map(r => 
          r.modelId === modelId ? errorResponse : r
        ));
        return errorResponse;
      }
    });

    await Promise.all(promises);
    setIsRunning(false);
  };

  const stopExecution = () => {
    setIsRunning(false);
    // In a real app, you would cancel the API requests here
  };

  const copyResponse = async (response: string) => {
    await navigator.clipboard.writeText(response);
  };

  const exportResults = () => {
    const data = {
      prompt,
      responses: responses.map(r => ({
        model: availableModels.find(m => m.id === r.modelId)?.name,
        response: r.response,
        latency: r.latency,
        cost: r.cost,
        timestamp: r.timestamp
      })),
      totalCost: responses.reduce((sum, r) => sum + r.cost, 0),
      timestamp: new Date()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `promptloop-results-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const totalCost = responses.reduce((sum, r) => sum + r.cost, 0);
  const avgLatency = responses.length > 0 
    ? responses.reduce((sum, r) => sum + r.latency, 0) / responses.length 
    : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prompt Runner</h1>
          <p className="text-gray-600">Test your prompts across multiple AI models</p>
        </div>
        <div className="flex items-center space-x-4">
          {responses.length > 0 && (
            <>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{Math.round(avgLatency)}ms avg</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <DollarSign className="w-4 h-4" />
                <span>${totalCost.toFixed(4)}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle>Prompt Input</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your prompt here..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="min-h-[120px]"
          />

          {/* Model Selection */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Select Models to Test</h3>
            {availableModels.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                <AlertCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No connected models found</p>
                <p className="text-sm">Configure your API keys in Settings</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {availableModels.map((model) => (
                  <label
                    key={model.id}
                    className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedModels.includes(model.id)
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedModels.includes(model.id)}
                      onChange={() => toggleModelSelection(model.id)}
                      className="mr-2"
                    />
                    <div className="min-w-0">
                      <div className="font-medium text-sm">{model.name}</div>
                      <div className="text-xs text-gray-500">{model.provider}</div>
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {!isRunning ? (
              <Button
                onClick={runPrompt}
                disabled={!prompt.trim() || selectedModels.length === 0}
                className="flex-1"
              >
                <Play className="w-4 h-4 mr-2" />
                Run Prompt ({selectedModels.length} models)
              </Button>
            ) : (
              <Button
                onClick={stopExecution}
                variant="destructive"
                className="flex-1"
              >
                <Square className="w-4 h-4 mr-2" />
                Stop Execution
              </Button>
            )}
            
            {responses.length > 0 && (
              <Button
                variant="outline"
                onClick={exportResults}
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {responses.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Results</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {responses.map((response) => {
              const model = availableModels.find(m => m.id === response.modelId);
              
              return (
                <Card key={response.modelId} className="h-fit">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">{model?.name}</h3>
                        {response.status === 'running' && (
                          <div className="flex items-center space-x-1">
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <span className="text-xs text-blue-600">Running...</span>
                          </div>
                        )}
                        {response.status === 'success' && (
                          <Badge variant="success" size="sm">Complete</Badge>
                        )}
                        {response.status === 'error' && (
                          <Badge variant="error" size="sm">Error</Badge>
                        )}
                      </div>
                      
                      {response.status === 'success' && (
                        <div className="flex items-center space-x-2">
                          <div className="text-xs text-gray-500">
                            {response.latency}ms • ${response.cost.toFixed(4)}
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyResponse(response.response)}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    {response.status === 'running' && (
                      <div className="flex items-center justify-center py-8">
                        <Zap className="w-6 h-6 text-blue-500 animate-pulse" />
                      </div>
                    )}
                    
                    {response.status === 'error' && (
                      <div className="text-center py-4 text-red-600">
                        <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                        <p className="font-medium">Request Failed</p>
                        <p className="text-sm">{response.error}</p>
                      </div>
                    )}
                    
                    {response.status === 'success' && (
                      <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800">
                          {response.response}
                        </pre>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
