// Content script for PromptLoop Chrome Extension
// Injects functionality into AI chat interfaces

(function() {
  'use strict';

  // Configuration for different AI platforms
  const platformConfig = {
    'chat.openai.com': {
      name: 'ChatGPT',
      textareaSelector: '#prompt-textarea, [data-testid="prompt-textarea"]',
      sendButtonSelector: '[data-testid="send-button"]',
      messagesSelector: '[data-testid="conversation-turn"]'
    },
    'claude.ai': {
      name: '<PERSON>',
      textareaSelector: '.ProseMirror, [contenteditable="true"]',
      sendButtonSelector: '[aria-label="Send Message"]',
      messagesSelector: '.font-claude-message'
    },
    'gemini.google.com': {
      name: 'Gemini',
      textareaSelector: '.ql-editor, [contenteditable="true"]',
      sendButtonSelector: '[aria-label="Send message"]',
      messagesSelector: '.model-response-text'
    }
  };

  // Get current platform configuration
  const currentPlatform = Object.keys(platformConfig).find(domain => 
    window.location.hostname.includes(domain)
  );

  if (!currentPlatform) {
    console.log('PromptLoop: Unsupported platform');
    return;
  }

  const config = platformConfig[currentPlatform];
  console.log(`PromptLoop: Initialized for ${config.name}`);

  // Create PromptLoop UI elements
  function createPromptLoopUI() {
    // Create floating action button
    const fab = document.createElement('div');
    fab.id = 'promptloop-fab';
    fab.innerHTML = `
      <div class="promptloop-fab-button" title="PromptLoop">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4"/>
          <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"/>
          <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"/>
          <path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"/>
          <path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"/>
        </svg>
      </div>
    `;

    // Create context menu
    const contextMenu = document.createElement('div');
    contextMenu.id = 'promptloop-context-menu';
    contextMenu.innerHTML = `
      <div class="promptloop-menu-item" data-action="enhance">
        <span>🪄 Enhance Prompt</span>
      </div>
      <div class="promptloop-menu-item" data-action="save">
        <span>💾 Save Prompt</span>
      </div>
      <div class="promptloop-menu-item" data-action="library">
        <span>📚 Open Library</span>
      </div>
      <div class="promptloop-menu-item" data-action="benchmark">
        <span>📊 Benchmark</span>
      </div>
    `;

    document.body.appendChild(fab);
    document.body.appendChild(contextMenu);

    // Add event listeners
    setupEventListeners(fab, contextMenu);
  }

  // Setup event listeners
  function setupEventListeners(fab, contextMenu) {
    // FAB click handler
    fab.addEventListener('click', (e) => {
      e.stopPropagation();
      const rect = fab.getBoundingClientRect();
      contextMenu.style.display = contextMenu.style.display === 'block' ? 'none' : 'block';
      contextMenu.style.top = `${rect.top - contextMenu.offsetHeight - 10}px`;
      contextMenu.style.left = `${rect.left}px`;
    });

    // Context menu item handlers
    contextMenu.addEventListener('click', (e) => {
      const action = e.target.closest('[data-action]')?.dataset.action;
      if (action) {
        handleAction(action);
        contextMenu.style.display = 'none';
      }
    });

    // Close context menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!fab.contains(e.target) && !contextMenu.contains(e.target)) {
        contextMenu.style.display = 'none';
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'e':
            if (e.shiftKey) {
              e.preventDefault();
              handleAction('enhance');
            }
            break;
          case 's':
            if (e.shiftKey) {
              e.preventDefault();
              handleAction('save');
            }
            break;
        }
      }
    });
  }

  // Handle actions
  function handleAction(action) {
    switch (action) {
      case 'enhance':
        enhanceCurrentPrompt();
        break;
      case 'save':
        saveCurrentPrompt();
        break;
      case 'library':
        openPromptLibrary();
        break;
      case 'benchmark':
        benchmarkCurrentPrompt();
        break;
    }
  }

  // Get current prompt text
  function getCurrentPrompt() {
    const textarea = document.querySelector(config.textareaSelector);
    if (textarea) {
      return textarea.value || textarea.textContent || textarea.innerText || '';
    }
    return '';
  }

  // Set prompt text
  function setPromptText(text) {
    const textarea = document.querySelector(config.textareaSelector);
    if (textarea) {
      if (textarea.value !== undefined) {
        textarea.value = text;
      } else {
        textarea.textContent = text;
      }
      
      // Trigger input event to notify the platform
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
      textarea.focus();
    }
  }

  // Enhance current prompt
  async function enhanceCurrentPrompt() {
    const currentPrompt = getCurrentPrompt();
    if (!currentPrompt.trim()) {
      showNotification('Please enter a prompt first', 'warning');
      return;
    }

    showNotification('Enhancing prompt...', 'info');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'enhance-prompt',
        prompt: currentPrompt
      });

      if (response.success) {
        setPromptText(response.result.enhanced);
        showNotification('Prompt enhanced successfully!', 'success');
      } else {
        showNotification('Enhancement failed: ' + response.error, 'error');
      }
    } catch (error) {
      showNotification('Enhancement failed: ' + error.message, 'error');
    }
  }

  // Save current prompt
  async function saveCurrentPrompt() {
    const currentPrompt = getCurrentPrompt();
    if (!currentPrompt.trim()) {
      showNotification('No prompt to save', 'warning');
      return;
    }

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'save-prompt',
        prompt: {
          original: currentPrompt,
          enhanced: currentPrompt,
          category: 'Saved from ' + config.name,
          tags: [config.name.toLowerCase(), 'manual-save'],
          source: config.name
        }
      });

      if (response.success) {
        showNotification('Prompt saved to library!', 'success');
      } else {
        showNotification('Save failed: ' + response.error, 'error');
      }
    } catch (error) {
      showNotification('Save failed: ' + error.message, 'error');
    }
  }

  // Open prompt library
  function openPromptLibrary() {
    chrome.runtime.sendMessage({
      action: 'open-tab',
      url: chrome.runtime.getURL('index.html#library')
    });
  }

  // Benchmark current prompt
  function benchmarkCurrentPrompt() {
    const currentPrompt = getCurrentPrompt();
    if (!currentPrompt.trim()) {
      showNotification('Please enter a prompt first', 'warning');
      return;
    }

    // Store prompt for benchmarking
    chrome.storage.local.set({
      pendingBenchmark: currentPrompt
    });

    chrome.runtime.sendMessage({
      action: 'open-tab',
      url: chrome.runtime.getURL('index.html#benchmark')
    });
  }

  // Show notification
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `promptloop-notification promptloop-notification-${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // Initialize when DOM is ready
  function initialize() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', createPromptLoopUI);
    } else {
      createPromptLoopUI();
    }
  }

  // Start initialization
  initialize();

  // Listen for messages from popup/background
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
      case 'get-current-prompt':
        sendResponse({ prompt: getCurrentPrompt() });
        break;
      case 'set-prompt':
        setPromptText(request.prompt);
        sendResponse({ success: true });
        break;
      case 'enhance-current':
        enhanceCurrentPrompt();
        sendResponse({ success: true });
        break;
    }
  });

})();
