import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Key, TestTube, CheckCircle, XCircle, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';

interface ModelConfig {
  id: string;
  name: string;
  provider: string;
  apiKey: string;
  endpoint?: string;
  status: 'connected' | 'disconnected' | 'testing' | 'error';
  lastTested?: Date;
  error?: string;
}

const defaultModels: Omit<ModelConfig, 'apiKey' | 'status'>[] = [
  {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    endpoint: 'https://api.openai.com/v1'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    endpoint: 'https://api.openai.com/v1'
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    provider: 'Anthropic',
    endpoint: 'https://api.anthropic.com'
  },
  {
    id: 'claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    endpoint: 'https://api.anthropic.com'
  },
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    endpoint: 'https://generativelanguage.googleapis.com'
  },
  {
    id: 'ollama-llama2',
    name: 'Llama 2 (Ollama)',
    provider: 'Ollama',
    endpoint: 'http://localhost:11434'
  }
];

export const SettingsPanel: React.FC = () => {
  const [models, setModels] = useState<ModelConfig[]>([]);
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Load saved configurations
    const loadConfigs = () => {
      try {
        const saved = localStorage.getItem('promptloop_models');
        if (saved) {
          setModels(JSON.parse(saved));
        } else {
          // Initialize with default models
          const initialModels = defaultModels.map(model => ({
            ...model,
            apiKey: '',
            status: 'disconnected' as const
          }));
          setModels(initialModels);
        }
      } catch (error) {
        console.error('Error loading model configs:', error);
      }
    };

    loadConfigs();
  }, []);

  useEffect(() => {
    // Save configurations whenever models change
    localStorage.setItem('promptloop_models', JSON.stringify(models));
  }, [models]);

  const updateModelConfig = (modelId: string, updates: Partial<ModelConfig>) => {
    setModels(prev => prev.map(model => 
      model.id === modelId ? { ...model, ...updates } : model
    ));
  };

  const testConnection = async (modelId: string) => {
    const model = models.find(m => m.id === modelId);
    if (!model || !model.apiKey) return;

    updateModelConfig(modelId, { status: 'testing' });

    try {
      // Mock API test - in real app, this would make actual API calls
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate random success/failure for demo
      const success = Math.random() > 0.3;
      
      if (success) {
        updateModelConfig(modelId, { 
          status: 'connected',
          lastTested: new Date(),
          error: undefined
        });
      } else {
        updateModelConfig(modelId, { 
          status: 'error',
          lastTested: new Date(),
          error: 'Invalid API key or connection failed'
        });
      }
    } catch (error) {
      updateModelConfig(modelId, { 
        status: 'error',
        error: 'Connection test failed'
      });
    }
  };

  const toggleApiKeyVisibility = (modelId: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  const getStatusIcon = (status: ModelConfig['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'testing':
        return <AlertCircle className="w-4 h-4 text-yellow-500 animate-pulse" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: ModelConfig['status']) => {
    switch (status) {
      case 'connected':
        return <Badge variant="success" size="sm">Connected</Badge>;
      case 'error':
        return <Badge variant="error" size="sm">Error</Badge>;
      case 'testing':
        return <Badge variant="warning" size="sm">Testing...</Badge>;
      default:
        return <Badge variant="secondary" size="sm">Not configured</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Configure your AI model connections and preferences</p>
        </div>
        <Settings className="w-6 h-6 text-gray-400" />
      </div>

      {/* API Keys Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Key className="w-5 h-5 mr-2 text-primary-600" />
            AI Model Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
            <p className="font-medium mb-1">🔒 Security Note</p>
            <p>API keys are stored locally in your browser and never sent to our servers. They are only used to make direct API calls to the respective AI providers.</p>
          </div>

          {models.map((model) => (
            <div key={model.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(model.status)}
                  <div>
                    <h3 className="font-medium text-gray-900">{model.name}</h3>
                    <p className="text-sm text-gray-500">{model.provider}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(model.status)}
                  {model.lastTested && (
                    <span className="text-xs text-gray-500">
                      Tested {model.lastTested.toLocaleString()}
                    </span>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    API Key
                  </label>
                  <div className="relative">
                    <Input
                      type={showApiKeys[model.id] ? 'text' : 'password'}
                      placeholder={`Enter your ${model.provider} API key`}
                      value={model.apiKey}
                      onChange={(e) => updateModelConfig(model.id, { 
                        apiKey: e.target.value,
                        status: e.target.value ? 'disconnected' : 'disconnected'
                      })}
                      className="pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => toggleApiKeyVisibility(model.id)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showApiKeys[model.id] ? (
                        <EyeOff className="w-4 h-4" />
                      ) : (
                        <Eye className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Endpoint
                  </label>
                  <Input
                    placeholder="API endpoint URL"
                    value={model.endpoint || ''}
                    onChange={(e) => updateModelConfig(model.id, { endpoint: e.target.value })}
                  />
                </div>
              </div>

              {model.error && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {model.error}
                </div>
              )}

              <div className="flex justify-end">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => testConnection(model.id)}
                  disabled={!model.apiKey || model.status === 'testing'}
                  loading={model.status === 'testing'}
                >
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Connection
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle>General Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Auto-save prompts</h4>
              <p className="text-sm text-gray-600">Automatically save enhanced prompts to your library</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Show notifications</h4>
              <p className="text-sm text-gray-600">Get notified when prompts are enhanced or saved</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Dark mode</h4>
              <p className="text-sm text-gray-600">Switch to dark theme</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle>Data Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Export Data</h4>
              <p className="text-sm text-gray-600">Download all your prompts and settings</p>
            </div>
            <Button variant="outline" size="sm">
              Export
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Clear All Data</h4>
              <p className="text-sm text-gray-600">Remove all prompts and reset settings</p>
            </div>
            <Button variant="destructive" size="sm">
              Clear Data
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
