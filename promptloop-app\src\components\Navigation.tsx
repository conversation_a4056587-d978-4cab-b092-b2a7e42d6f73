import React from 'react';
import { clsx } from 'clsx';
import { Zap, BookOpen, Play, BarChart3, Settings } from 'lucide-react';

type TabType = 'enhancer' | 'library' | 'runner' | 'benchmark' | 'settings';

interface NavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const navigationItems = [
  {
    id: 'enhancer' as TabType,
    label: 'Prompt Enhancer',
    icon: Zap,
    description: 'Turn ideas into prompts'
  },
  {
    id: 'library' as TabType,
    label: 'Prompt Library',
    icon: BookOpen,
    description: 'Saved prompts'
  },
  {
    id: 'runner' as TabType,
    label: 'Prompt Runner',
    icon: Play,
    description: 'Test prompts'
  },
  {
    id: 'benchmark' as TabType,
    label: 'Benchmark',
    icon: BarChart3,
    description: 'Compare models'
  },
  {
    id: 'settings' as TabType,
    label: 'Settings',
    icon: Settings,
    description: 'Configure app'
  }
];

export const Navigation: React.FC<NavigationProps> = ({ activeTab, onTabChange }) => {
  return (
    <nav className="w-64 bg-white border-r border-gray-200 h-screen overflow-y-auto">
      <div className="p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onTabChange(item.id)}
                  className={clsx(
                    'w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )}
                >
                  <Icon className={clsx(
                    'mr-3 h-5 w-5',
                    isActive ? 'text-primary-600' : 'text-gray-400'
                  )} />
                  <div className="text-left">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </button>
              </li>
            );
          })}
        </ul>
      </div>
    </nav>
  );
};
