// Background script for PromptLoop Chrome Extension

// Installation and update handling
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    // First time installation
    console.log('PromptLoop installed');
    
    // Set default settings
    chrome.storage.local.set({
      promptloop_settings: {
        autoSave: true,
        notifications: true,
        darkMode: false
      }
    });

    // Open welcome page
    chrome.tabs.create({
      url: chrome.runtime.getURL('index.html?welcome=true')
    });
  } else if (details.reason === 'update') {
    console.log('PromptLoop updated');
  }
});

// Context menu setup
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'enhance-prompt',
    title: 'Enhance with PromptLoop',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'open-promptloop',
    title: 'Open PromptLoop',
    contexts: ['page']
  });
});

// Context menu click handler
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'enhance-prompt') {
    // Send selected text to enhancement
    if (info.selectionText) {
      chrome.storage.local.set({
        pendingEnhancement: info.selectionText
      });
      
      chrome.tabs.create({
        url: chrome.runtime.getURL('index.html?enhance=true')
      });
    }
  } else if (info.menuItemId === 'open-promptloop') {
    chrome.tabs.create({
      url: chrome.runtime.getURL('index.html')
    });
  }
});

// Message handling from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'enhance-prompt':
      handlePromptEnhancement(request.prompt)
        .then(result => sendResponse({ success: true, result }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Keep message channel open for async response

    case 'save-prompt':
      savePrompt(request.prompt)
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'get-prompts':
      getPrompts()
        .then(prompts => sendResponse({ success: true, prompts }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    case 'run-prompt':
      runPromptOnModels(request.prompt, request.models)
        .then(results => sendResponse({ success: true, results }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;

    default:
      sendResponse({ success: false, error: 'Unknown action' });
  }
});

// Prompt enhancement function
async function handlePromptEnhancement(prompt) {
  try {
    // This would normally call your enhancement API
    // For now, we'll use a mock enhancement
    const enhanced = await mockEnhancePrompt(prompt);
    
    // Save to storage
    const result = await chrome.storage.local.get(['promptloop_prompts']);
    const prompts = result.promptloop_prompts || [];
    
    const newPrompt = {
      id: Date.now().toString(),
      original: prompt,
      enhanced: enhanced.enhanced,
      category: enhanced.category,
      tags: enhanced.tags,
      timestamp: new Date().toISOString()
    };
    
    prompts.unshift(newPrompt);
    await chrome.storage.local.set({ promptloop_prompts: prompts });
    
    return newPrompt;
  } catch (error) {
    console.error('Enhancement failed:', error);
    throw error;
  }
}

// Mock prompt enhancement (replace with actual API call)
async function mockEnhancePrompt(input) {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  let enhanced = '';
  let category = 'General';
  let tags = ['general'];

  if (input.toLowerCase().includes('email')) {
    category = 'Email Marketing';
    tags = ['email', 'marketing'];
    enhanced = `Act as an expert email marketing specialist. Write a compelling email that:\n\n**Context:** ${input}\n\n**Requirements:**\n- Keep it under 150 words\n- Include a clear value proposition\n- Add a specific call-to-action\n- Use a conversational, professional tone\n\n**Structure:**\n1. Attention-grabbing subject line\n2. Brief personal connection\n3. Clear problem identification\n4. Your solution/value proposition\n5. Specific next step/CTA`;
  } else if (input.toLowerCase().includes('code')) {
    category = 'Development';
    tags = ['coding', 'programming'];
    enhanced = `Act as a senior software engineer. Help with this coding task:\n\n**Task:** ${input}\n\n**Requirements:**\n- Write clean, readable code\n- Include proper error handling\n- Add meaningful comments\n- Follow best practices\n- Consider performance\n\n**Provide:**\n1. Complete solution\n2. Explanation of approach\n3. Alternative solutions\n4. Testing recommendations\n5. Edge cases to consider`;
  } else {
    enhanced = `Act as a knowledgeable assistant. Help with:\n\n**Request:** ${input}\n\n**Approach:**\n- Provide comprehensive information\n- Break down complex topics\n- Include practical examples\n- Offer actionable steps\n- Consider different perspectives\n\n**Structure:**\n1. Clear understanding of request\n2. Detailed explanation/solution\n3. Supporting examples\n4. Implementation steps\n5. Additional considerations`;
  }

  return { enhanced, category, tags };
}

// Save prompt function
async function savePrompt(promptData) {
  const result = await chrome.storage.local.get(['promptloop_prompts']);
  const prompts = result.promptloop_prompts || [];
  
  prompts.unshift({
    ...promptData,
    id: Date.now().toString(),
    timestamp: new Date().toISOString()
  });
  
  await chrome.storage.local.set({ promptloop_prompts: prompts });
}

// Get prompts function
async function getPrompts() {
  const result = await chrome.storage.local.get(['promptloop_prompts']);
  return result.promptloop_prompts || [];
}

// Run prompt on multiple models
async function runPromptOnModels(prompt, models) {
  const results = [];
  
  for (const model of models) {
    try {
      // This would make actual API calls to the models
      // For now, we'll simulate the responses
      const response = await simulateModelResponse(model, prompt);
      results.push(response);
    } catch (error) {
      results.push({
        modelId: model.id,
        error: error.message,
        status: 'error'
      });
    }
  }
  
  return results;
}

// Simulate model responses
async function simulateModelResponse(model, prompt) {
  // Simulate API delay
  const delay = Math.random() * 2000 + 500;
  await new Promise(resolve => setTimeout(resolve, delay));
  
  return {
    modelId: model.id,
    modelName: model.name,
    response: `Mock response from ${model.name} for prompt: "${prompt.substring(0, 50)}..."`,
    latency: delay,
    cost: Math.random() * 0.05,
    status: 'success',
    timestamp: new Date().toISOString()
  };
}

// Notification helper
function showNotification(title, message) {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'icons/icon48.png',
    title: title,
    message: message
  });
}

// Keep service worker alive
chrome.runtime.onConnect.addListener((port) => {
  port.onDisconnect.addListener(() => {
    // Handle disconnect
  });
});
