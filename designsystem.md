## Product Requirements Document (PRD)

### Product Name: PromptLoop

### Description:

PromptLoop is a Chrome extension + micro SaaS platform that turns vague ideas into powerful prompts, and lets users save, organize, reuse, and share high-quality prompts across any AI tool. It also allows users to test those prompts instantly across various LLMs like OpenAI, Gemini, Claude, Grok, and even local models like Ollama — all in one click. Think of it as your universal PromptOps layer.

---

## Executive Summary

PromptLoop is a universal command center for prompts. It helps AI users — creators, marketers, product managers, founders — go from raw ideas like "do market research" to fully structured, beautiful prompts tailored to specific use cases.

It solves the problem of inconsistent or poor prompt writing and the lack of centralized prompt memory. Users can generate, save, organize, and even benchmark prompts across multiple AI tools from a single interface.

**Target audience:** AI power users, indie hackers, product teams, students.

**Key differentiator:** PromptLoop combines world-class meta prompt generation with a universal multi-model testing playground and a highly usable prompt library — right inside your browser.

---

## Current Situation

Right now, users are stuck in inefficient workflows:

- Jotting prompts down in Notion or Docs
- Forgetting what worked and what didn’t
- Rewriting prompts from scratch each time
- Copy-pasting from one tab to another to test across models

This leads to:

- Frustration
- Waste of time
- Missed opportunities for better AI output

Users want to:

- Save good prompts without friction
- Reuse winning prompts fast
- Run side-by-side tests across AI tools

---

## Problem Statement

The biggest problem PromptLoop solves is **PromptOps Fatigue** — the overhead users face in writing, testing, storing, and managing prompts.

**User pain:**

- Prompt writing is inconsistent
- Prompt reuse is manual
- Prompt testing across tools is clunky

**Impact:**

- Time wasted
- Poor AI output
- Drop in productivity

**Hidden cost:**

- Knowledge leakage (users forget what worked)

**Why this matters:** Prompt quality is the biggest leverage point in any AI workflow. Optimizing prompts = exponential time saved.

---

## Competitive Analysis

| Product            | Pros                        | Cons                         |
| ------------------ | --------------------------- | ---------------------------- |
| AIPRM              | Large community prompt list | Static, can’t personalize    |
| PromptPerfect      | Optimizes prompts           | No memory or history         |
| FlowGPT            | Community sharing           | No tool integrations         |
| Superpower ChatGPT | Adds utilities to ChatGPT   | Only works with ChatGPT      |
| Promptable.ai      | Templates + teams           | No auto-prompting or testing |

**Gaps PromptLoop Fills:**

- Prompt generation from scratch
- Prompt memory + tagging
- Multi-model compatibility (Claude, Gemini, Ollama, etc.)
- Chrome-native UX

---

## Solution Overview

### Core MVP Features

1. ✅ **Prompt Enhancer**: Converts vague tasks into great prompts
2. ✅ **Prompt Library**: Save, tag, folder, search, add notes
3. ✅ **Multi-Model Testing**: Run prompt on GPT, Claude, Gemini, etc.
4. ✅ **Prompt Tuner**: Adjust syntax based on model
5. ✅ **Prompt Playground**: View results from each model side-by-side

### Tech Stack

- **Frontend**: React + Tailwind (extension + dashboard)
- **Storage**: Supabase (auth, prompt storage)
- **Model Router**: Custom router with support for each API format
- **Extension API**: Chrome APIs for inject/capture/save
- **Prompt Preview**: html2canvas to export or share prompts

---

## Onboarding + Engagement Flow

1. 🧠 **User Installs PromptLoop Extension**
2. 👋 **Welcome Screen**
   - "You give the idea. We’ll write the prompt."
   - Sign up with Google or Magic Link (Supabase)
3. 🔗 **Connect Your AI Tools**
   - Dropdown to connect OpenAI / Claude / Gemini / Ollama / etc.
   - Paste API keys or detect Ollama on localhost
   - Each tool gets a test ping for validation
4. 🪄 **Use the Prompt Enhancer**
   - User types: “Write a job description for a PM in edtech”
   - PromptLoop generates full prompt: "Act as a recruitment consultant..."
5. 💡 **Run Prompt Across Tools**
   - User selects: Run this on → GPT-4 + Claude + Gemini
   - See responses side-by-side, with latency & cost shown
6. 💾 **Save to Prompt Library**
   - Tag: "Hiring", Folder: "PM Jobs"
   - Add optional notes: “Claude gave best summary.”
7. 🔁 **Reuse Prompt Later**
   - Access saved prompts from popup or dashboard
   - Export as PNG or copy to clipboard

---

## 🔁 PromptLoop – Universal PromptOps Layer

**“Write once. Prompt anywhere.”**

### Supported AI Tools (v0 MVP)

| Model          | Integration Method        |
| -------------- | ------------------------- |
| OpenAI GPT-4   | API Key                   |
| Claude 3       | API Key                   |
| Gemini Pro     | OAuth / API Key           |
| Grok (X)       | Proxy if public           |
| Qwen, DeepSeek | Model Hub APIs            |
| Ollama (Local) | Detect on localhost:11434 |

### Prompt Test Flow

- Select prompt
- Choose models
- Hit "Run"
- Results render in model tabs or side-by-side view
- You can retry, tweak, or save changes

### Prompt Tuner Features

- Adds system message if model supports
- Changes format based on provider needs

---

## UX/UI Simplification for Developers

| Action        | User Experience                       |
| ------------- | ------------------------------------- |
| Add API Keys  | One form → Dropdown + Input field     |
| Save Prompt   | 1-click Save → Prompt, Notes, Tags    |
| Run Prompt    | Button → Select Tools → Get Responses |
| View Library  | Click icon → See cards w/ filters     |
| Export Prompt | Copy / PNG / Markdown                 |

---

## MVP Success Metrics

- ✅ 1st prompt created within 3 minutes of install
- ✅ 5 prompts saved per user in first week
- ✅ At least 1 prompt run across 2+ tools

---

## Pricing

- **Free**: 10 prompts/day, 3 saved prompts
- **Pro**: $8/mo → Unlimited prompts, save, export, tune
- **Team**: $29/mo → Shared library, admin control

---

## Risks

- Model APIs change → use abstraction layer
- Rate limits → throttle + cache
- Chrome restrictions → also launch web fallback

---

## Dev Notes (Read This First!) ✅

- Every prompt = object with:
  - `id`, `text`, `tags`, `folder`, `note`, `created_by`, `created_at`
- Store in Supabase (or IndexedDB offline fallback)
- All model APIs must go via a unified router
- Ollama is optional but enabled by default if installed
- Testing panel is separate tab in extension (like Postman)
- Use React state for prompts, and Chrome localStorage for auth/session backup

---

Let’s go build PromptLoop:  
**The productivity OS for prompt nerds.**
