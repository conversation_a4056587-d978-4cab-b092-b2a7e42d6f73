import React, { useState, useEffect } from 'react';
import { BarChart3, Clock, DollarSign, Star, TrendingUp, Download } from 'lucide-react';
import { Button } from './ui/Button';
import { Textarea } from './ui/Textarea';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';

interface BenchmarkResult {
  modelId: string;
  modelName: string;
  response: string;
  latency: number;
  cost: number;
  qualityScore: number;
  timestamp: Date;
}

interface BenchmarkSession {
  id: string;
  prompt: string;
  results: BenchmarkResult[];
  timestamp: Date;
}

export const BenchmarkPlayground: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [sessions, setSessions] = useState<BenchmarkSession[]>([]);
  const [currentSession, setCurrentSession] = useState<BenchmarkSession | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [availableModels, setAvailableModels] = useState<any[]>([]);

  useEffect(() => {
    // Load available models
    const loadModels = () => {
      try {
        const saved = localStorage.getItem('promptloop_models');
        if (saved) {
          const models = JSON.parse(saved);
          setAvailableModels(models.filter((m: any) => m.status === 'connected'));
        }
      } catch (error) {
        console.error('Error loading models:', error);
      }
    };

    // Load benchmark sessions
    const loadSessions = () => {
      try {
        const saved = localStorage.getItem('promptloop_benchmarks');
        if (saved) {
          const parsedSessions = JSON.parse(saved).map((session: any) => ({
            ...session,
            timestamp: new Date(session.timestamp),
            results: session.results.map((result: any) => ({
              ...result,
              timestamp: new Date(result.timestamp)
            }))
          }));
          setSessions(parsedSessions);
        }
      } catch (error) {
        console.error('Error loading benchmark sessions:', error);
      }
    };

    loadModels();
    loadSessions();
  }, []);

  const mockBenchmarkRun = async (_prompt: string): Promise<BenchmarkResult[]> => {
    const results: BenchmarkResult[] = [];
    
    for (const model of availableModels.slice(0, 4)) { // Limit to 4 models for demo
      const startTime = Date.now();
      
      // Simulate API call
      const delay = Math.random() * 2000 + 500; // 0.5-2.5 seconds
      await new Promise(resolve => setTimeout(resolve, delay));
      
      const latency = Date.now() - startTime;
      const cost = Math.random() * 0.03 + 0.001; // $0.001 - $0.031
      const qualityScore = Math.random() * 30 + 70; // 70-100 quality score

      // Generate mock response based on model
      let response = '';
      if (model.id.includes('gpt-4')) {
        response = `**GPT-4 Analysis:**\n\nThis is a comprehensive response demonstrating GPT-4's advanced reasoning capabilities. The model provides structured, detailed analysis with clear formatting and actionable insights.\n\nKey strengths:\n• Superior reasoning and logic\n• Excellent formatting and structure\n• Comprehensive coverage of topics\n• High accuracy and reliability\n\nThis response showcases why GPT-4 is considered one of the most capable language models available.`;
      } else if (model.id.includes('claude')) {
        response = `I appreciate your thoughtful prompt. Here's my analysis:\n\nClaude's approach emphasizes safety, nuance, and helpful responses. I aim to provide balanced perspectives while being direct about limitations.\n\nStrengths of this response:\n- Thoughtful consideration of context\n- Balanced and nuanced viewpoint\n- Clear acknowledgment of limitations\n- Focus on being genuinely helpful\n\nI strive to be honest about what I can and cannot do, while providing maximum value within those constraints.`;
      } else if (model.id.includes('gemini')) {
        response = `## Gemini Response Analysis\n\n**Processing Complete** ✅\n\n### Key Insights:\n🔍 **Analysis**: Multi-modal understanding applied\n⚡ **Speed**: Optimized for fast response generation\n🎯 **Accuracy**: High confidence in output quality\n\n### Response Characteristics:\n- Structured markdown formatting\n- Visual elements and emojis\n- Technical precision\n- Clear categorization\n\nThis demonstrates Gemini's strength in providing well-structured, visually appealing responses with technical accuracy.`;
      } else {
        response = `Response from ${model.name}:\n\nThis model provides a solid response to your prompt, demonstrating its training and capabilities. The output shows good understanding of the request and provides relevant information.\n\nCharacteristics:\n- Clear communication\n- Relevant content\n- Appropriate length\n- Good structure\n\nThis represents the model's ability to handle various types of prompts effectively.`;
      }

      results.push({
        modelId: model.id,
        modelName: model.name,
        response,
        latency,
        cost,
        qualityScore,
        timestamp: new Date()
      });
    }

    return results;
  };

  const runBenchmark = async () => {
    if (!prompt.trim() || availableModels.length === 0) return;

    setIsRunning(true);
    
    try {
      const results = await mockBenchmarkRun(prompt);
      
      const newSession: BenchmarkSession = {
        id: Date.now().toString(),
        prompt,
        results,
        timestamp: new Date()
      };

      setSessions(prev => [newSession, ...prev]);
      setCurrentSession(newSession);
      
      // Save to localStorage
      const updatedSessions = [newSession, ...sessions];
      localStorage.setItem('promptloop_benchmarks', JSON.stringify(updatedSessions));
      
    } catch (error) {
      console.error('Benchmark failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const exportBenchmark = (session: BenchmarkSession) => {
    const data = {
      prompt: session.prompt,
      results: session.results,
      summary: {
        totalCost: session.results.reduce((sum, r) => sum + r.cost, 0),
        avgLatency: session.results.reduce((sum, r) => sum + r.latency, 0) / session.results.length,
        avgQuality: session.results.reduce((sum, r) => sum + r.qualityScore, 0) / session.results.length,
        bestModel: session.results.reduce((best, current) => 
          current.qualityScore > best.qualityScore ? current : best
        ).modelName,
        fastestModel: session.results.reduce((fastest, current) => 
          current.latency < fastest.latency ? current : fastest
        ).modelName
      },
      timestamp: session.timestamp
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `benchmark-${session.id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityBadge = (score: number) => {
    if (score >= 90) return <Badge variant="success" size="sm">Excellent</Badge>;
    if (score >= 80) return <Badge variant="warning" size="sm">Good</Badge>;
    return <Badge variant="error" size="sm">Fair</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Benchmark Playground</h1>
          <p className="text-gray-600">Compare AI models side-by-side with detailed metrics</p>
        </div>
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-5 h-5 text-primary-600" />
          <Badge variant="secondary">{sessions.length} benchmarks</Badge>
        </div>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle>Benchmark Setup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter a prompt to benchmark across all connected models..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="min-h-[100px]"
          />
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {availableModels.length} models available for benchmarking
            </div>
            <Button
              onClick={runBenchmark}
              disabled={!prompt.trim() || availableModels.length === 0 || isRunning}
              loading={isRunning}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              {isRunning ? 'Running Benchmark...' : 'Run Benchmark'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Session Results */}
      {currentSession && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Latest Benchmark Results</CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => exportBenchmark(currentSession)}
                >
                  <Download className="w-3 h-3 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <Clock className="w-4 h-4 text-gray-600 mr-1" />
                </div>
                <div className="text-lg font-semibold">
                  {Math.round(currentSession.results.reduce((sum, r) => sum + r.latency, 0) / currentSession.results.length)}ms
                </div>
                <div className="text-xs text-gray-600">Avg Latency</div>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <DollarSign className="w-4 h-4 text-gray-600 mr-1" />
                </div>
                <div className="text-lg font-semibold">
                  ${currentSession.results.reduce((sum, r) => sum + r.cost, 0).toFixed(4)}
                </div>
                <div className="text-xs text-gray-600">Total Cost</div>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <Star className="w-4 h-4 text-gray-600 mr-1" />
                </div>
                <div className="text-lg font-semibold">
                  {Math.round(currentSession.results.reduce((sum, r) => sum + r.qualityScore, 0) / currentSession.results.length)}
                </div>
                <div className="text-xs text-gray-600">Avg Quality</div>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <TrendingUp className="w-4 h-4 text-gray-600 mr-1" />
                </div>
                <div className="text-lg font-semibold">
                  {currentSession.results.reduce((best, current) => 
                    current.qualityScore > best.qualityScore ? current : best
                  ).modelName.split(' ')[0]}
                </div>
                <div className="text-xs text-gray-600">Best Model</div>
              </div>
            </div>

            {/* Model Comparison */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">Model Comparison</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {currentSession.results
                  .sort((a, b) => b.qualityScore - a.qualityScore)
                  .map((result, index) => (
                  <Card key={result.modelId} className={`${index === 0 ? 'ring-2 ring-green-200' : ''}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{result.modelName}</h4>
                          {index === 0 && <Badge variant="success" size="sm">Best</Badge>}
                        </div>
                        {getQualityBadge(result.qualityScore)}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {result.latency}ms
                        </div>
                        <div className="flex items-center">
                          <DollarSign className="w-3 h-3 mr-1" />
                          ${result.cost.toFixed(4)}
                        </div>
                        <div className={`flex items-center ${getQualityColor(result.qualityScore)}`}>
                          <Star className="w-3 h-3 mr-1" />
                          {Math.round(result.qualityScore)}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="bg-gray-50 rounded-lg p-3 max-h-48 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-xs text-gray-800">
                          {result.response}
                        </pre>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Previous Sessions */}
      {sessions.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Previous Benchmarks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sessions.slice(1, 6).map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => setCurrentSession(session)}
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {session.prompt.substring(0, 80)}...
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <span>{session.results.length} models</span>
                      <span>{session.timestamp.toLocaleDateString()}</span>
                      <span>
                        Avg: {Math.round(session.results.reduce((sum, r) => sum + r.qualityScore, 0) / session.results.length)} quality
                      </span>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      exportBenchmark(session);
                    }}
                  >
                    <Download className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
