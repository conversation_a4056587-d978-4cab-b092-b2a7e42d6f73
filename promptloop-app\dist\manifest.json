{"manifest_version": 3, "name": "PromptLoop", "version": "1.0.0", "description": "Universal PromptOps Platform - Turn vague ideas into powerful prompts and test them across multiple AI models", "permissions": ["storage", "activeTab", "tabs", "scripting"], "host_permissions": ["https://api.openai.com/*", "https://api.anthropic.com/*", "https://generativelanguage.googleapis.com/*", "http://localhost:11434/*"], "action": {"default_popup": "popup.html", "default_title": "PromptLoop", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://chat.openai.com/*", "https://claude.ai/*", "https://gemini.google.com/*"], "js": ["content.js"], "css": ["content.css"]}], "web_accessible_resources": [{"resources": ["index.html", "assets/*"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "options_page": "index.html"}