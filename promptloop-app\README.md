# PromptLoop - Universal PromptOps Platform

PromptLoop is a Chrome extension + micro SaaS platform that turns vague ideas into powerful prompts and lets users save, organize, reuse, and share high-quality prompts across any AI tool.

## 🚀 Features

- **Prompt Enhancer**: Transform vague ideas into structured, professional prompts
- **Prompt Library**: Save, organize, and manage your prompts with tags and folders
- **Multi-Model Testing**: Run prompts across multiple AI models (GPT, Claude, Gemini, Ollama)
- **Benchmark Playground**: Compare AI models side-by-side with detailed metrics
- **Chrome Extension**: Seamless integration with popular AI chat interfaces
- **Authentication**: Secure user management with Google sign-in and magic links

## 🛠️ Development Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Chrome browser (for extension testing)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd promptloop-app
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173/`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## 🔧 Troubleshooting

### Blank Screen Issues

If you see a blank white screen when opening the application:

1. **Check the development server**:
   ```bash
   cd promptloop-app
   npm run dev
   ```
   Ensure the server starts successfully and shows:
   ```
   ➜  Local:   http://localhost:5173/
   ➜  Network: use --host to expose
   ```

2. **Check browser console**:
   - Open Developer Tools (F12)
   - Look for JavaScript errors in the Console tab
   - Check the Network tab for failed requests

3. **Verify build process**:
   ```bash
   npm run build
   ```
   Ensure no TypeScript or build errors occur.

4. **Common fixes**:
   - Clear browser cache and reload
   - Check if all dependencies are installed: `npm install`
   - Verify Node.js version: `node --version` (should be v16+)

### Authentication Issues

If the login screen doesn't work:

1. Check localStorage in browser DevTools
2. Verify AuthContext is properly wrapped around the app
3. Check console for authentication-related errors

### Component Loading Issues

If specific components don't load:

1. Check import paths in the browser console
2. Verify all component files exist in `src/components/`
3. Check for TypeScript compilation errors

## 📁 Project Structure

```
promptloop-app/
├── public/                 # Static files and Chrome extension files
│   ├── manifest.json      # Chrome extension manifest
│   ├── background.js      # Extension background script
│   ├── content.js         # Extension content script
│   └── content.css        # Extension styles
├── src/
│   ├── components/        # React components
│   │   ├── auth/         # Authentication components
│   │   ├── ui/           # Reusable UI components
│   │   ├── PromptEnhancer.tsx
│   │   ├── PromptLibrary.tsx
│   │   ├── PromptRunner.tsx
│   │   ├── BenchmarkPlayground.tsx
│   │   ├── SettingsPanel.tsx
│   │   └── Navigation.tsx
│   ├── contexts/         # React contexts
│   │   └── AuthContext.tsx
│   ├── App.tsx           # Main application component
│   ├── main.tsx          # Application entry point
│   ├── popup.tsx         # Extension popup entry point
│   ├── PopupApp.tsx      # Extension popup component
│   └── index.css         # Global styles
├── index.html            # Main application HTML
├── popup.html            # Extension popup HTML
└── package.json          # Dependencies and scripts
```

## 🎯 Usage

### Main Application

1. **Prompt Enhancement**:
   - Navigate to the Prompt Enhancer tab
   - Enter a vague idea or request
   - Click "Enhance Prompt" to get a structured version
   - Save the enhanced prompt to your library

2. **Prompt Library**:
   - View all saved prompts
   - Search and filter by tags or categories
   - Edit prompt metadata (tags, folders, notes)
   - Export prompts for sharing

3. **Prompt Testing**:
   - Use the Prompt Runner to test prompts across multiple models
   - Compare responses side-by-side
   - View latency and cost metrics

4. **Benchmarking**:
   - Run comprehensive benchmarks across all connected models
   - View detailed performance metrics
   - Export benchmark results

### Chrome Extension

1. Install the extension by loading the `dist/` folder in Chrome
2. Configure API keys in the Settings panel
3. Use the floating action button on AI chat interfaces
4. Right-click selected text to enhance with PromptLoop

## 🔑 API Configuration

Configure your AI model API keys in the Settings panel:

- **OpenAI**: Get your API key from https://platform.openai.com/
- **Anthropic**: Get your API key from https://console.anthropic.com/
- **Google**: Get your API key from https://makersuite.google.com/
- **Ollama**: Ensure Ollama is running locally on port 11434

## 🚀 Deployment

### Web Application

Deploy the built files from `dist/` to any static hosting service:
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Chrome Extension

1. Build the project: `npm run build`
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked" and select the `dist/` folder

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and test thoroughly
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
