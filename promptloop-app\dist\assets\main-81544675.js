import{c as N,r as m,j as e,R as z,Z as _,B as ee,S as se,a as G,b as ie}from"./index-9328dd03.js";const O=N("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),$=N("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),le=N("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),ce=N("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),oe=N("Chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]]),F=N("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),J=N("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),H=N("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),K=N("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),de=N("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),me=N("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),xe=N("Folder",[["path",{d:"M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z",key:"1fr9dc"}]]),he=N("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]),te=N("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),pe=N("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),ue=N("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),W=N("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),ge=N("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),ae=N("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),ye=N("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),fe=N("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),je=N("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),ve=N("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Q=N("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),Ne=N("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),Z=N("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),be=N("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),we=N("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),ke=N("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Se=N("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),X=N("Wand2",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z",key:"1bcowg"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]),Ce=N("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Pe=N("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function re(t){var r,s,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(s=re(t[r]))&&(n&&(n+=" "),n+=s)}else for(s in t)t[s]&&(n&&(n+=" "),n+=s);return n}function R(){for(var t,r,s=0,n="",l=arguments.length;s<l;s++)(t=arguments[s])&&(r=re(t))&&(n&&(n+=" "),n+=r);return n}const k=m.forwardRef(({className:t,variant:r="primary",size:s="md",loading:n=!1,disabled:l,children:b,...d},g)=>{const S="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",o={primary:"bg-primary-600 text-white hover:bg-primary-700",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200",outline:"border border-secondary-300 bg-transparent hover:bg-secondary-50",ghost:"hover:bg-secondary-100 hover:text-secondary-900",destructive:"bg-red-600 text-white hover:bg-red-700"},h={sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"};return e.jsxs("button",{className:R(S,o[r],h[s],t),ref:g,disabled:l||n,...d,children:[n&&e.jsx(te,{className:"mr-2 h-4 w-4 animate-spin"}),b]})});k.displayName="Button";const D=z.forwardRef(({className:t,label:r,error:s,helperText:n,...l},b)=>e.jsxs("div",{className:"space-y-2",children:[r&&e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:r}),e.jsx("textarea",{className:R("flex min-h-[80px] w-full rounded-md border border-secondary-300 bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s&&"border-red-500 focus-visible:ring-red-500",t),ref:b,...l}),s&&e.jsx("p",{className:"text-sm text-red-600",children:s}),n&&!s&&e.jsx("p",{className:"text-sm text-gray-500",children:n})]}));D.displayName="Textarea";const E=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("div",{ref:n,className:R("rounded-lg border border-secondary-200 bg-white p-6 shadow-sm",t),...s,children:r}));E.displayName="Card";const L=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("div",{ref:n,className:R("flex flex-col space-y-1.5 pb-4",t),...s,children:r}));L.displayName="CardHeader";const I=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("h3",{ref:n,className:R("text-lg font-semibold leading-none tracking-tight",t),...s,children:r}));I.displayName="CardTitle";const Me=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("p",{ref:n,className:R("text-sm text-secondary-600",t),...s,children:r}));Me.displayName="CardDescription";const A=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("div",{ref:n,className:R("pt-0",t),...s,children:r}));A.displayName="CardContent";const Ee=z.forwardRef(({className:t,children:r,...s},n)=>e.jsx("div",{ref:n,className:R("flex items-center pt-4",t),...s,children:r}));Ee.displayName="CardFooter";const M=z.forwardRef(({className:t,variant:r="default",size:s="md",children:n,...l},b)=>{const d="inline-flex items-center rounded-full font-medium",g={default:"bg-primary-100 text-primary-800",secondary:"bg-secondary-100 text-secondary-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800"},S={sm:"px-2 py-1 text-xs",md:"px-2.5 py-0.5 text-sm"};return e.jsx("div",{className:R(d,g[r],S[s],t),ref:b,...l,children:n})});M.displayName="Badge";const Y=()=>{const[t,r]=m.useState(""),[s,n]=m.useState(null),[l,b]=m.useState(!1),[d,g]=m.useState(!1),S=async j=>{await new Promise(y=>setTimeout(y,2e3));let T="",i="General",p=[];return j.toLowerCase().includes("email")||j.toLowerCase().includes("cold")?(i="Email Marketing",p=["email","marketing","outreach"],T=`Act as an expert email marketing specialist. Write a compelling cold email that:

**Context & Purpose:**
- ${j}

**Requirements:**
- Keep it under 150 words
- Include a clear value proposition
- Add a specific call-to-action
- Use a conversational, professional tone
- Personalize with recipient research

**Structure:**
1. Attention-grabbing subject line
2. Brief personal connection or compliment
3. Clear problem identification
4. Your solution/value proposition
5. Specific next step/CTA

**Output Format:**
- Subject line
- Email body
- Follow-up suggestions

Please make it authentic and avoid overly salesy language.`):j.toLowerCase().includes("code")||j.toLowerCase().includes("programming")?(i="Development",p=["coding","programming","development"],T=`Act as a senior software engineer and code reviewer. Help with the following coding task:

**Task:** ${j}

**Requirements:**
- Write clean, readable, and maintainable code
- Include proper error handling
- Add meaningful comments for complex logic
- Follow best practices and conventions
- Consider performance implications

**Please provide:**
1. Complete code solution
2. Explanation of approach
3. Alternative solutions if applicable
4. Testing recommendations
5. Potential edge cases to consider

**Code Quality Standards:**
- Use descriptive variable names
- Keep functions focused and small
- Handle edge cases appropriately
- Include type hints where applicable`):j.toLowerCase().includes("content")||j.toLowerCase().includes("blog")?(i="Content Creation",p=["content","writing","blog"],T=`Act as a professional content strategist and copywriter. Create engaging content for:

**Brief:** ${j}

**Content Strategy:**
- Target audience analysis
- Key messaging and tone
- SEO considerations
- Engagement optimization

**Deliverables:**
1. Compelling headline options (3-5 variations)
2. Detailed content outline
3. Key points and supporting arguments
4. Call-to-action suggestions
5. Distribution strategy recommendations

**Quality Standards:**
- Original, valuable insights
- Clear, scannable structure
- Actionable takeaways
- Optimized for target platform
- Brand voice consistency

Please ensure the content provides genuine value and stands out in a crowded content landscape.`):(i="General",p=["general","assistant"],T=`Act as a knowledgeable and helpful assistant. Please help with the following request:

**Request:** ${j}

**Approach:**
- Provide comprehensive and accurate information
- Break down complex topics into digestible parts
- Include practical examples where relevant
- Offer actionable next steps
- Consider different perspectives

**Response Structure:**
1. Clear understanding of the request
2. Detailed explanation or solution
3. Supporting examples or evidence
4. Practical implementation steps
5. Additional resources or considerations

**Quality Standards:**
- Accurate and up-to-date information
- Clear and concise communication
- Practical and actionable advice
- Appropriate depth for the topic
- Professional and helpful tone

Please ensure your response is thorough yet accessible.`),{original:j,enhanced:T,category:i,tags:p,timestamp:new Date}},o=async()=>{if(t.trim()){b(!0);try{const j=await S(t);n(j)}catch(j){console.error("Enhancement failed:",j)}finally{b(!1)}}},h=async()=>{s&&await navigator.clipboard.writeText(s.enhanced)},w=async()=>{if(s){g(!0);try{const j=JSON.parse(localStorage.getItem("promptloop_prompts")||"[]");j.push({id:Date.now().toString(),...s}),localStorage.setItem("promptloop_prompts",JSON.stringify(j)),await new Promise(T=>setTimeout(T,1e3))}catch(j){console.error("Save failed:",j)}finally{g(!1)}}},C=()=>{r(""),n(null)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Prompt Enhancer"}),e.jsx("p",{className:"text-gray-600",children:"Transform your ideas into powerful, structured prompts"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Q,{className:"w-5 h-5 text-primary-600"}),e.jsx("span",{className:"text-sm text-gray-500",children:"AI-Powered Enhancement"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(E,{children:[e.jsx(L,{children:e.jsxs(I,{className:"flex items-center",children:[e.jsx(X,{className:"w-5 h-5 mr-2 text-primary-600"}),"Your Idea"]})}),e.jsxs(A,{className:"space-y-4",children:[e.jsx(D,{placeholder:`Enter your vague idea here... 

Examples:
• 'Write a cold email for my AI startup'
• 'Help me debug this React component'
• 'Create a blog post about productivity'
• 'Explain quantum computing simply'`,value:t,onChange:j=>r(j.target.value),className:"min-h-[200px]"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(k,{onClick:o,loading:l,disabled:!t.trim(),className:"flex-1",children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),l?"Enhancing...":"Enhance Prompt"]}),(t||s)&&e.jsx(k,{variant:"outline",onClick:C,children:e.jsx(fe,{className:"w-4 h-4"})})]})]})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsxs(I,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"w-5 h-5 mr-2 text-primary-600"}),"Enhanced Prompt"]}),s&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{variant:"secondary",children:s.category}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx(k,{size:"sm",variant:"outline",onClick:h,children:e.jsx(J,{className:"w-3 h-3"})}),e.jsx(k,{size:"sm",onClick:w,loading:d,children:e.jsx(je,{className:"w-3 h-3"})})]})]})]})}),e.jsx(A,{children:s?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex flex-wrap gap-1",children:s.tags.map(j=>e.jsx(M,{size:"sm",variant:"secondary",children:j},j))}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4 max-h-[300px] overflow-y-auto",children:e.jsx("pre",{className:"whitespace-pre-wrap text-sm text-gray-800 font-mono",children:s.enhanced})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Enhanced on ",s.timestamp.toLocaleString()]})]}):e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx(X,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),e.jsx("p",{children:"Your enhanced prompt will appear here"}),e.jsx("p",{className:"text-sm mt-1",children:'Enter an idea and click "Enhance Prompt" to get started'})]})})]})]})]})},q=m.forwardRef(({className:t,label:r,error:s,helperText:n,...l},b)=>e.jsxs("div",{className:"space-y-2",children:[r&&e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:r}),e.jsx("input",{className:R("flex h-10 w-full rounded-md border border-secondary-300 bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s&&"border-red-500 focus-visible:ring-red-500",t),ref:b,...l}),s&&e.jsx("p",{className:"text-sm text-red-600",children:s}),n&&!s&&e.jsx("p",{className:"text-sm text-gray-500",children:n})]}));q.displayName="Input";const Te=({isOpen:t,onClose:r,title:s,children:n,size:l="md"})=>{if(m.useEffect(()=>{const d=g=>{g.key==="Escape"&&r()};return t&&(document.addEventListener("keydown",d),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",d),document.body.style.overflow="unset"}},[t,r]),!t)return null;const b={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"};return e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:r}),e.jsxs("div",{className:R("relative bg-white rounded-lg shadow-xl w-full mx-4",b[l]),children:[s&&e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:s}),e.jsx(k,{variant:"ghost",size:"sm",onClick:r,className:"p-1 h-auto",children:e.jsx(Pe,{className:"w-4 h-4"})})]}),e.jsx("div",{className:"p-6",children:n})]})]})},Ae=()=>{const[t,r]=m.useState([]),[s,n]=m.useState(""),[l,b]=m.useState("all"),[d,g]=m.useState("all"),[S,o]=m.useState(!1),[h,w]=m.useState(null),[C,j]=m.useState("newest");m.useEffect(()=>{(()=>{try{const v=JSON.parse(localStorage.getItem("promptloop_prompts")||"[]").map(P=>({...P,timestamp:new Date(P.timestamp),lastUsed:P.lastUsed?new Date(P.lastUsed):void 0}));r(v)}catch(f){console.error("Error loading prompts:",f)}})()},[]),m.useEffect(()=>{localStorage.setItem("promptloop_prompts",JSON.stringify(t))},[t]);const T=["all",...new Set(t.map(a=>a.category))],i=["all",...new Set(t.map(a=>a.folder).filter(Boolean))],p=t.filter(a=>{const f=s===""||a.original.toLowerCase().includes(s.toLowerCase())||a.enhanced.toLowerCase().includes(s.toLowerCase())||a.tags.some(B=>B.toLowerCase().includes(s.toLowerCase())),v=l==="all"||a.category===l,P=d==="all"||a.folder===d;return f&&v&&P}).sort((a,f)=>{var v,P;switch(C){case"oldest":return a.timestamp.getTime()-f.timestamp.getTime();case"mostUsed":return(((v=f.lastUsed)==null?void 0:v.getTime())||0)-(((P=a.lastUsed)==null?void 0:P.getTime())||0);default:return f.timestamp.getTime()-a.timestamp.getTime()}}),y=async a=>{await navigator.clipboard.writeText(a.enhanced),r(f=>f.map(v=>v.id===a.id?{...v,lastUsed:new Date}:v))},c=a=>{r(f=>f.filter(v=>v.id!==a))},u=a=>{w(a),o(!0)},x=a=>{r(f=>f.map(v=>v.id===a.id?a:v)),o(!1),w(null)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Prompt Library"}),e.jsx("p",{className:"text-gray-600",children:"Manage and organize your saved prompts"})]}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(M,{variant:"secondary",children:[p.length," prompts"]})})]}),e.jsx(E,{children:e.jsx(A,{className:"space-y-4",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ve,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(q,{placeholder:"Search prompts, tags, or content...",value:s,onChange:a=>n(a.target.value),className:"pl-10"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("select",{value:l,onChange:a=>b(a.target.value),className:"input w-auto",children:T.map(a=>e.jsx("option",{value:a,children:a==="all"?"All Categories":a},a))}),e.jsx("select",{value:d,onChange:a=>g(a.target.value),className:"input w-auto",children:i.map(a=>e.jsx("option",{value:a,children:a==="all"?"All Folders":a},a))}),e.jsxs("select",{value:C,onChange:a=>j(a.target.value),className:"input w-auto",children:[e.jsx("option",{value:"newest",children:"Newest First"}),e.jsx("option",{value:"oldest",children:"Oldest First"}),e.jsx("option",{value:"mostUsed",children:"Most Used"})]})]})]})})}),p.length===0?e.jsx(E,{children:e.jsxs(A,{className:"text-center py-12",children:[e.jsx(ee,{className:"w-16 h-16 mx-auto mb-4 text-gray-300"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No prompts found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:s||l!=="all"||d!=="all"?"Try adjusting your search or filters":"Start by creating your first prompt in the Prompt Enhancer"})]})}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.map(a=>e.jsxs(E,{className:"hover:shadow-md transition-shadow",children:[e.jsx(L,{className:"pb-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx(M,{size:"sm",className:"mb-2",children:a.category}),e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:a.original})]}),e.jsxs("div",{className:"flex space-x-1 ml-2",children:[e.jsx(k,{size:"sm",variant:"ghost",onClick:()=>y(a),title:"Copy prompt",children:e.jsx(J,{className:"w-3 h-3"})}),e.jsx(k,{size:"sm",variant:"ghost",onClick:()=>u(a),title:"Edit prompt",children:e.jsx(ge,{className:"w-3 h-3"})}),e.jsx(k,{size:"sm",variant:"ghost",onClick:()=>c(a.id),title:"Delete prompt",children:e.jsx(we,{className:"w-3 h-3 text-red-500"})})]})]})}),e.jsx(A,{className:"pt-0",children:e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex flex-wrap gap-1",children:a.tags.map(f=>e.jsx(M,{size:"sm",variant:"secondary",children:f},f))}),a.folder&&e.jsxs("div",{className:"flex items-center text-xs text-gray-500",children:[e.jsx(xe,{className:"w-3 h-3 mr-1"}),a.folder]}),e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"w-3 h-3 mr-1"}),a.timestamp.toLocaleDateString()]}),a.lastUsed&&e.jsxs("div",{children:["Last used: ",a.lastUsed.toLocaleDateString()]})]}),a.notes&&e.jsx("p",{className:"text-xs text-gray-600 bg-gray-50 p-2 rounded",children:a.notes})]})})]},a.id))}),e.jsx(Te,{isOpen:S,onClose:()=>{o(!1),w(null)},title:"Edit Prompt",size:"lg",children:h&&e.jsx(Le,{prompt:h,onSave:x,onCancel:()=>{o(!1),w(null)}})})]})},Le=({prompt:t,onSave:r,onCancel:s})=>{const[n,l]=m.useState({category:t.category,tags:t.tags.join(", "),folder:t.folder||"",notes:t.notes||""}),b=d=>{d.preventDefault(),r({...t,category:n.category,tags:n.tags.split(",").map(g=>g.trim()).filter(Boolean),folder:n.folder||void 0,notes:n.notes||void 0})};return e.jsxs("form",{onSubmit:b,className:"space-y-4",children:[e.jsx(q,{label:"Category",value:n.category,onChange:d=>l(g=>({...g,category:d.target.value})),required:!0}),e.jsx(q,{label:"Tags",value:n.tags,onChange:d=>l(g=>({...g,tags:d.target.value})),helperText:"Separate tags with commas"}),e.jsx(q,{label:"Folder",value:n.folder,onChange:d=>l(g=>({...g,folder:d.target.value}))}),e.jsx(D,{label:"Notes",value:n.notes,onChange:d=>l(g=>({...g,notes:d.target.value})),placeholder:"Add any notes about this prompt..."}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(k,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),e.jsx(k,{type:"submit",children:"Save Changes"})]})]})},Re=()=>{const[t,r]=m.useState(""),[s,n]=m.useState([]),[l,b]=m.useState([]),[d,g]=m.useState([]),[S,o]=m.useState(!1);m.useEffect(()=>{(()=>{try{const u=localStorage.getItem("promptloop_models");if(u){const a=JSON.parse(u).filter(f=>f.status==="connected");b(a)}}catch(u){console.error("Error loading models:",u)}})()},[]);const h=c=>{n(u=>u.includes(c)?u.filter(x=>x!==c):[...u,c])},w=async(c,u)=>{const x=l.find(U=>U.id===c),a=Date.now(),f=Math.random()*3e3+1e3;await new Promise(U=>setTimeout(U,f));const v=Date.now()-a;if(Math.random()<.1)return{modelId:c,response:"",latency:v,cost:0,status:"error",error:"API rate limit exceeded",timestamp:new Date};let P="";const B=Math.random()*.05+.001;return c.includes("gpt")?P=`**GPT Response:**

Based on your prompt, here's a comprehensive response:

${u.length>100?"This is a detailed analysis of your complex prompt. ":"Here's a concise response to your query. "}

Key points:
• Structured approach to the problem
• Practical implementation steps  
• Considerations for best practices
• Potential challenges and solutions

The response demonstrates GPT's strength in providing structured, detailed answers with clear formatting and actionable insights.

*Generated by ${(x==null?void 0:x.name)||"GPT Model"}*`:c.includes("claude")?P=`I'd be happy to help with your request.

${u.length>100?"Looking at your detailed prompt, I can provide a thorough analysis:":"For your query, here's my response:"}

My approach would be:

1. **Understanding the context**: ${u.substring(0,50)}...
2. **Key considerations**: Safety, accuracy, and helpfulness
3. **Recommended solution**: A balanced approach that addresses your needs

I aim to be helpful while being thoughtful about potential implications. Is there any specific aspect you'd like me to elaborate on?

*Response from ${(x==null?void 0:x.name)||"Claude"}*`:c.includes("gemini")?P=`## Gemini Analysis

**Query Processing**: ✅ Complete
**Safety Check**: ✅ Passed
**Response Generation**: ✅ Ready

### Response:

${u.length>100?"Your comprehensive prompt requires a multi-faceted response:":"Quick response to your query:"}

🔍 **Analysis**: The prompt shows clear intent and structure
⚡ **Solution**: Here's my recommended approach
🎯 **Outcome**: Expected results and next steps

### Technical Details:
- Processing time: ${v}ms
- Confidence score: ${Math.floor(Math.random()*20+80)}%
- Safety rating: High

*Powered by ${(x==null?void 0:x.name)||"Gemini"}*`:P=`Response from ${(x==null?void 0:x.name)||"AI Model"}:

${u.length>100?"Analyzing your detailed prompt...":"Processing your query..."}

Here's my response based on the input provided. The model has processed your request and generated this output according to its training and capabilities.

Key insights:
- Relevant information extracted from prompt
- Contextual understanding applied
- Response tailored to your needs

This demonstrates the model's ability to understand and respond to various types of prompts effectively.`,{modelId:c,response:P,latency:v,cost:B,status:"success",timestamp:new Date}},C=async()=>{if(!t.trim()||s.length===0)return;o(!0),g([]);const c=s.map(x=>({modelId:x,response:"",latency:0,cost:0,status:"running",timestamp:new Date}));g(c);const u=s.map(async x=>{try{const a=await w(x,t);return g(f=>f.map(v=>v.modelId===x?a:v)),a}catch{const f={modelId:x,response:"",latency:0,cost:0,status:"error",error:"Request failed",timestamp:new Date};return g(v=>v.map(P=>P.modelId===x?f:P)),f}});await Promise.all(u),o(!1)},j=()=>{o(!1)},T=async c=>{await navigator.clipboard.writeText(c)},i=()=>{const c={prompt:t,responses:d.map(f=>{var v;return{model:(v=l.find(P=>P.id===f.modelId))==null?void 0:v.name,response:f.response,latency:f.latency,cost:f.cost,timestamp:f.timestamp}}),totalCost:d.reduce((f,v)=>f+v.cost,0),timestamp:new Date},u=new Blob([JSON.stringify(c,null,2)],{type:"application/json"}),x=URL.createObjectURL(u),a=document.createElement("a");a.href=x,a.download=`promptloop-results-${Date.now()}.json`,a.click(),URL.revokeObjectURL(x)},p=d.reduce((c,u)=>c+u.cost,0),y=d.length>0?d.reduce((c,u)=>c+u.latency,0)/d.length:0;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Prompt Runner"}),e.jsx("p",{className:"text-gray-600",children:"Test your prompts across multiple AI models"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:d.length>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[e.jsx(F,{className:"w-4 h-4"}),e.jsxs("span",{children:[Math.round(y),"ms avg"]})]}),e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[e.jsx(H,{className:"w-4 h-4"}),e.jsxs("span",{children:["$",p.toFixed(4)]})]})]})})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsx(I,{children:"Prompt Input"})}),e.jsxs(A,{className:"space-y-4",children:[e.jsx(D,{placeholder:"Enter your prompt here...",value:t,onChange:c=>r(c.target.value),className:"min-h-[120px]"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select Models to Test"}),l.length===0?e.jsxs("div",{className:"text-center py-4 text-gray-500",children:[e.jsx(O,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),e.jsx("p",{children:"No connected models found"}),e.jsx("p",{className:"text-sm",children:"Configure your API keys in Settings"})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:l.map(c=>e.jsxs("label",{className:`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${s.includes(c.id)?"border-primary-500 bg-primary-50":"border-gray-200 hover:border-gray-300"}`,children:[e.jsx("input",{type:"checkbox",checked:s.includes(c.id),onChange:()=>h(c.id),className:"mr-2"}),e.jsxs("div",{className:"min-w-0",children:[e.jsx("div",{className:"font-medium text-sm",children:c.name}),e.jsx("div",{className:"text-xs text-gray-500",children:c.provider})]})]},c.id))})]}),e.jsxs("div",{className:"flex space-x-2",children:[S?e.jsxs(k,{onClick:j,variant:"destructive",className:"flex-1",children:[e.jsx(Ne,{className:"w-4 h-4 mr-2"}),"Stop Execution"]}):e.jsxs(k,{onClick:C,disabled:!t.trim()||s.length===0,className:"flex-1",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"}),"Run Prompt (",s.length," models)"]}),d.length>0&&e.jsxs(k,{variant:"outline",onClick:i,children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Export"]})]})]})]}),d.length>0&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Results"}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:d.map(c=>{const u=l.find(x=>x.id===c.modelId);return e.jsxs(E,{className:"h-fit",children:[e.jsx(L,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h3",{className:"font-medium",children:u==null?void 0:u.name}),c.status==="running"&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-blue-600",children:"Running..."})]}),c.status==="success"&&e.jsx(M,{variant:"success",size:"sm",children:"Complete"}),c.status==="error"&&e.jsx(M,{variant:"error",size:"sm",children:"Error"})]}),c.status==="success"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:[c.latency,"ms • $",c.cost.toFixed(4)]}),e.jsx(k,{size:"sm",variant:"ghost",onClick:()=>T(c.response),children:e.jsx(J,{className:"w-3 h-3"})})]})]})}),e.jsxs(A,{children:[c.status==="running"&&e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(_,{className:"w-6 h-6 text-blue-500 animate-pulse"})}),c.status==="error"&&e.jsxs("div",{className:"text-center py-4 text-red-600",children:[e.jsx(O,{className:"w-8 h-8 mx-auto mb-2"}),e.jsx("p",{className:"font-medium",children:"Request Failed"}),e.jsx("p",{className:"text-sm",children:c.error})]}),c.status==="success"&&e.jsx("div",{className:"bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto",children:e.jsx("pre",{className:"whitespace-pre-wrap text-sm text-gray-800",children:c.response})})]})]},c.modelId)})})]})]})},Ie=()=>{const[t,r]=m.useState(""),[s,n]=m.useState([]),[l,b]=m.useState(null),[d,g]=m.useState(!1),[S,o]=m.useState([]);m.useEffect(()=>{const i=()=>{try{const y=localStorage.getItem("promptloop_models");if(y){const c=JSON.parse(y);o(c.filter(u=>u.status==="connected"))}}catch(y){console.error("Error loading models:",y)}},p=()=>{try{const y=localStorage.getItem("promptloop_benchmarks");if(y){const c=JSON.parse(y).map(u=>({...u,timestamp:new Date(u.timestamp),results:u.results.map(x=>({...x,timestamp:new Date(x.timestamp)}))}));n(c)}}catch(y){console.error("Error loading benchmark sessions:",y)}};i(),p()},[]);const h=async i=>{const p=[];for(const y of S.slice(0,4)){const c=Date.now(),u=Math.random()*2e3+500;await new Promise(P=>setTimeout(P,u));const x=Date.now()-c,a=Math.random()*.03+.001,f=Math.random()*30+70;let v="";y.id.includes("gpt-4")?v=`**GPT-4 Analysis:**

This is a comprehensive response demonstrating GPT-4's advanced reasoning capabilities. The model provides structured, detailed analysis with clear formatting and actionable insights.

Key strengths:
• Superior reasoning and logic
• Excellent formatting and structure
• Comprehensive coverage of topics
• High accuracy and reliability

This response showcases why GPT-4 is considered one of the most capable language models available.`:y.id.includes("claude")?v=`I appreciate your thoughtful prompt. Here's my analysis:

Claude's approach emphasizes safety, nuance, and helpful responses. I aim to provide balanced perspectives while being direct about limitations.

Strengths of this response:
- Thoughtful consideration of context
- Balanced and nuanced viewpoint
- Clear acknowledgment of limitations
- Focus on being genuinely helpful

I strive to be honest about what I can and cannot do, while providing maximum value within those constraints.`:y.id.includes("gemini")?v=`## Gemini Response Analysis

**Processing Complete** ✅

### Key Insights:
🔍 **Analysis**: Multi-modal understanding applied
⚡ **Speed**: Optimized for fast response generation
🎯 **Accuracy**: High confidence in output quality

### Response Characteristics:
- Structured markdown formatting
- Visual elements and emojis
- Technical precision
- Clear categorization

This demonstrates Gemini's strength in providing well-structured, visually appealing responses with technical accuracy.`:v=`Response from ${y.name}:

This model provides a solid response to your prompt, demonstrating its training and capabilities. The output shows good understanding of the request and provides relevant information.

Characteristics:
- Clear communication
- Relevant content
- Appropriate length
- Good structure

This represents the model's ability to handle various types of prompts effectively.`,p.push({modelId:y.id,modelName:y.name,response:v,latency:x,cost:a,qualityScore:f,timestamp:new Date})}return p},w=async()=>{if(!(!t.trim()||S.length===0)){g(!0);try{const i=await h(t),p={id:Date.now().toString(),prompt:t,results:i,timestamp:new Date};n(c=>[p,...c]),b(p);const y=[p,...s];localStorage.setItem("promptloop_benchmarks",JSON.stringify(y))}catch(i){console.error("Benchmark failed:",i)}finally{g(!1)}}},C=i=>{const p={prompt:i.prompt,results:i.results,summary:{totalCost:i.results.reduce((x,a)=>x+a.cost,0),avgLatency:i.results.reduce((x,a)=>x+a.latency,0)/i.results.length,avgQuality:i.results.reduce((x,a)=>x+a.qualityScore,0)/i.results.length,bestModel:i.results.reduce((x,a)=>a.qualityScore>x.qualityScore?a:x).modelName,fastestModel:i.results.reduce((x,a)=>a.latency<x.latency?a:x).modelName},timestamp:i.timestamp},y=new Blob([JSON.stringify(p,null,2)],{type:"application/json"}),c=URL.createObjectURL(y),u=document.createElement("a");u.href=c,u.download=`benchmark-${i.id}.json`,u.click(),URL.revokeObjectURL(c)},j=i=>i>=90?"text-green-600":i>=80?"text-yellow-600":"text-red-600",T=i=>i>=90?e.jsx(M,{variant:"success",size:"sm",children:"Excellent"}):i>=80?e.jsx(M,{variant:"warning",size:"sm",children:"Good"}):e.jsx(M,{variant:"error",size:"sm",children:"Fair"});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Benchmark Playground"}),e.jsx("p",{className:"text-gray-600",children:"Compare AI models side-by-side with detailed metrics"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-primary-600"}),e.jsxs(M,{variant:"secondary",children:[s.length," benchmarks"]})]})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsx(I,{children:"Benchmark Setup"})}),e.jsxs(A,{className:"space-y-4",children:[e.jsx(D,{placeholder:"Enter a prompt to benchmark across all connected models...",value:t,onChange:i=>r(i.target.value),className:"min-h-[100px]"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:[S.length," models available for benchmarking"]}),e.jsxs(k,{onClick:w,disabled:!t.trim()||S.length===0||d,loading:d,children:[e.jsx($,{className:"w-4 h-4 mr-2"}),d?"Running Benchmark...":"Run Benchmark"]})]})]})]}),l&&e.jsxs(E,{children:[e.jsx(L,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(I,{children:"Latest Benchmark Results"}),e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs(k,{size:"sm",variant:"outline",onClick:()=>C(l),children:[e.jsx(K,{className:"w-3 h-3 mr-1"}),"Export"]})})]})}),e.jsxs(A,{children:[e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"flex items-center justify-center mb-1",children:e.jsx(F,{className:"w-4 h-4 text-gray-600 mr-1"})}),e.jsxs("div",{className:"text-lg font-semibold",children:[Math.round(l.results.reduce((i,p)=>i+p.latency,0)/l.results.length),"ms"]}),e.jsx("div",{className:"text-xs text-gray-600",children:"Avg Latency"})]}),e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"flex items-center justify-center mb-1",children:e.jsx(H,{className:"w-4 h-4 text-gray-600 mr-1"})}),e.jsxs("div",{className:"text-lg font-semibold",children:["$",l.results.reduce((i,p)=>i+p.cost,0).toFixed(4)]}),e.jsx("div",{className:"text-xs text-gray-600",children:"Total Cost"})]}),e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"flex items-center justify-center mb-1",children:e.jsx(Z,{className:"w-4 h-4 text-gray-600 mr-1"})}),e.jsx("div",{className:"text-lg font-semibold",children:Math.round(l.results.reduce((i,p)=>i+p.qualityScore,0)/l.results.length)}),e.jsx("div",{className:"text-xs text-gray-600",children:"Avg Quality"})]}),e.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[e.jsx("div",{className:"flex items-center justify-center mb-1",children:e.jsx(ke,{className:"w-4 h-4 text-gray-600 mr-1"})}),e.jsx("div",{className:"text-lg font-semibold",children:l.results.reduce((i,p)=>p.qualityScore>i.qualityScore?p:i).modelName.split(" ")[0]}),e.jsx("div",{className:"text-xs text-gray-600",children:"Best Model"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Model Comparison"}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:l.results.sort((i,p)=>p.qualityScore-i.qualityScore).map((i,p)=>e.jsxs(E,{className:`${p===0?"ring-2 ring-green-200":""}`,children:[e.jsxs(L,{className:"pb-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("h4",{className:"font-medium",children:i.modelName}),p===0&&e.jsx(M,{variant:"success",size:"sm",children:"Best"})]}),T(i.qualityScore)]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{className:"w-3 h-3 mr-1"}),i.latency,"ms"]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(H,{className:"w-3 h-3 mr-1"}),"$",i.cost.toFixed(4)]}),e.jsxs("div",{className:`flex items-center ${j(i.qualityScore)}`,children:[e.jsx(Z,{className:"w-3 h-3 mr-1"}),Math.round(i.qualityScore)]})]})]}),e.jsx(A,{children:e.jsx("div",{className:"bg-gray-50 rounded-lg p-3 max-h-48 overflow-y-auto",children:e.jsx("pre",{className:"whitespace-pre-wrap text-xs text-gray-800",children:i.response})})})]},i.modelId))})]})]})]}),s.length>1&&e.jsxs(E,{children:[e.jsx(L,{children:e.jsx(I,{children:"Previous Benchmarks"})}),e.jsx(A,{children:e.jsx("div",{className:"space-y-3",children:s.slice(1,6).map(i=>e.jsxs("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer",onClick:()=>b(i),children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 truncate",children:[i.prompt.substring(0,80),"..."]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 mt-1",children:[e.jsxs("span",{children:[i.results.length," models"]}),e.jsx("span",{children:i.timestamp.toLocaleDateString()}),e.jsxs("span",{children:["Avg: ",Math.round(i.results.reduce((p,y)=>p+y.qualityScore,0)/i.results.length)," quality"]})]})]}),e.jsx(k,{size:"sm",variant:"ghost",onClick:p=>{p.stopPropagation(),C(i)},children:e.jsx(K,{className:"w-3 h-3"})})]},i.id))})})]})]})},qe=[{id:"gpt-4",name:"GPT-4",provider:"OpenAI",endpoint:"https://api.openai.com/v1"},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",endpoint:"https://api.openai.com/v1"},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",endpoint:"https://api.anthropic.com"},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",endpoint:"https://api.anthropic.com"},{id:"gemini-pro",name:"Gemini Pro",provider:"Google",endpoint:"https://generativelanguage.googleapis.com"},{id:"ollama-llama2",name:"Llama 2 (Ollama)",provider:"Ollama",endpoint:"http://localhost:11434"}],ze=()=>{const[t,r]=m.useState([]),[s,n]=m.useState({});m.useEffect(()=>{(()=>{try{const h=localStorage.getItem("promptloop_models");if(h)r(JSON.parse(h));else{const w=qe.map(C=>({...C,apiKey:"",status:"disconnected"}));r(w)}}catch(h){console.error("Error loading model configs:",h)}})()},[]),m.useEffect(()=>{localStorage.setItem("promptloop_models",JSON.stringify(t))},[t]);const l=(o,h)=>{r(w=>w.map(C=>C.id===o?{...C,...h}:C))},b=async o=>{const h=t.find(w=>w.id===o);if(!(!h||!h.apiKey)){l(o,{status:"testing"});try{await new Promise(C=>setTimeout(C,2e3)),Math.random()>.3?l(o,{status:"connected",lastTested:new Date,error:void 0}):l(o,{status:"error",lastTested:new Date,error:"Invalid API key or connection failed"})}catch{l(o,{status:"error",error:"Connection test failed"})}}},d=o=>{n(h=>({...h,[o]:!h[o]}))},g=o=>{switch(o){case"connected":return e.jsx(ce,{className:"w-4 h-4 text-green-500"});case"error":return e.jsx(Ce,{className:"w-4 h-4 text-red-500"});case"testing":return e.jsx(O,{className:"w-4 h-4 text-yellow-500 animate-pulse"});default:return e.jsx(O,{className:"w-4 h-4 text-gray-400"})}},S=o=>{switch(o){case"connected":return e.jsx(M,{variant:"success",size:"sm",children:"Connected"});case"error":return e.jsx(M,{variant:"error",size:"sm",children:"Error"});case"testing":return e.jsx(M,{variant:"warning",size:"sm",children:"Testing..."});default:return e.jsx(M,{variant:"secondary",size:"sm",children:"Not configured"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure your AI model connections and preferences"})]}),e.jsx(se,{className:"w-6 h-6 text-gray-400"})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsxs(I,{className:"flex items-center",children:[e.jsx(he,{className:"w-5 h-5 mr-2 text-primary-600"}),"AI Model Configuration"]})}),e.jsxs(A,{className:"space-y-6",children:[e.jsxs("div",{className:"text-sm text-gray-600 bg-blue-50 p-3 rounded-md",children:[e.jsx("p",{className:"font-medium mb-1",children:"🔒 Security Note"}),e.jsx("p",{children:"API keys are stored locally in your browser and never sent to our servers. They are only used to make direct API calls to the respective AI providers."})]}),t.map(o=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[g(o.status),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:o.name}),e.jsx("p",{className:"text-sm text-gray-500",children:o.provider})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[S(o.status),o.lastTested&&e.jsxs("span",{className:"text-xs text-gray-500",children:["Tested ",o.lastTested.toLocaleString()]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"API Key"}),e.jsxs("div",{className:"relative",children:[e.jsx(q,{type:s[o.id]?"text":"password",placeholder:`Enter your ${o.provider} API key`,value:o.apiKey,onChange:h=>l(o.id,{apiKey:h.target.value,status:(h.target.value,"disconnected")}),className:"pr-10"}),e.jsx("button",{type:"button",onClick:()=>d(o.id),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:s[o.id]?e.jsx(de,{className:"w-4 h-4"}):e.jsx(me,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Endpoint"}),e.jsx(q,{placeholder:"API endpoint URL",value:o.endpoint||"",onChange:h=>l(o.id,{endpoint:h.target.value})})]})]}),o.error&&e.jsx("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:o.error}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(k,{size:"sm",variant:"outline",onClick:()=>b(o.id),disabled:!o.apiKey||o.status==="testing",loading:o.status==="testing",children:[e.jsx(be,{className:"w-4 h-4 mr-2"}),"Test Connection"]})})]},o.id))]})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsx(I,{children:"General Settings"})}),e.jsxs(A,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Auto-save prompts"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Automatically save enhanced prompts to your library"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Show notifications"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Get notified when prompts are enhanced or saved"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Dark mode"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Switch to dark theme"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})]})]}),e.jsxs(E,{children:[e.jsx(L,{children:e.jsx(I,{children:"Data Management"})}),e.jsxs(A,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Export Data"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download all your prompts and settings"})]}),e.jsx(k,{variant:"outline",size:"sm",children:"Export"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900",children:"Clear All Data"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Remove all prompts and reset settings"})]}),e.jsx(k,{variant:"destructive",size:"sm",children:"Clear Data"})]})]})]})]})},De=[{id:"enhancer",label:"Prompt Enhancer",icon:_,description:"Turn ideas into prompts"},{id:"library",label:"Prompt Library",icon:ee,description:"Saved prompts"},{id:"runner",label:"Prompt Runner",icon:ae,description:"Test prompts"},{id:"benchmark",label:"Benchmark",icon:$,description:"Compare models"},{id:"settings",label:"Settings",icon:se,description:"Configure app"}],Oe=({activeTab:t,onTabChange:r})=>e.jsx("nav",{className:"w-64 bg-white border-r border-gray-200 h-screen overflow-y-auto",children:e.jsx("div",{className:"p-4",children:e.jsx("ul",{className:"space-y-2",children:De.map(s=>{const n=s.icon,l=t===s.id;return e.jsx("li",{children:e.jsxs("button",{onClick:()=>r(s.id),className:R("w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",l?"bg-primary-100 text-primary-700 border-r-2 border-primary-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[e.jsx(n,{className:R("mr-3 h-5 w-5",l?"text-primary-600":"text-gray-400")}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-medium",children:s.label}),e.jsx("div",{className:"text-xs text-gray-500",children:s.description})]})]})},s.id)})})})}),ne=m.createContext(void 0),V=()=>{const t=m.useContext(ne);if(t===void 0)throw new Error("useAuth must be used within an AuthProvider");return t},Be=({children:t})=>{const[r,s]=m.useState(null),[n,l]=m.useState(!0);m.useEffect(()=>{(()=>{try{const w=localStorage.getItem("promptloop_user");w&&s(JSON.parse(w))}catch(w){console.error("Error loading saved user:",w),localStorage.removeItem("promptloop_user")}finally{l(!1)}})()},[]);const o={user:r,isLoading:n,signIn:async(h,w)=>{l(!0);try{await new Promise(j=>setTimeout(j,1e3));const C={id:"1",email:h,name:h.split("@")[0],avatar:`https://api.dicebear.com/7.x/avataaars/svg?seed=${h}`};s(C),localStorage.setItem("promptloop_user",JSON.stringify(C))}catch{throw new Error("Authentication failed")}finally{l(!1)}},signInWithGoogle:async()=>{l(!0);try{await new Promise(w=>setTimeout(w,1e3));const h={id:"1",email:"<EMAIL>",name:"John Doe",avatar:"https://api.dicebear.com/7.x/avataaars/svg?seed=google"};s(h),localStorage.setItem("promptloop_user",JSON.stringify(h))}catch{throw new Error("Google sign-in failed")}finally{l(!1)}},signOut:async()=>{s(null),localStorage.removeItem("promptloop_user")},sendMagicLink:async h=>{await new Promise(w=>setTimeout(w,1e3)),console.log(`Magic link sent to ${h}`)}};return e.jsx(ne.Provider,{value:o,children:t})},Ue=({onToggleMode:t})=>{const[r,s]=m.useState(""),[n,l]=m.useState(""),[b,d]=m.useState(!1),[g,S]=m.useState(""),[o,h]=m.useState(!1),{signIn:w,signInWithGoogle:C,sendMagicLink:j}=V(),T=async y=>{if(y.preventDefault(),!!r){d(!0),S("");try{await w(r,n)}catch{S("Authentication failed. Please try again.")}finally{d(!1)}}},i=async()=>{d(!0),S("");try{await C()}catch{S("Google sign-in failed. Please try again.")}finally{d(!1)}},p=async()=>{if(!r){S("Please enter your email address");return}d(!0),S("");try{await j(r),h(!0)}catch{S("Failed to send magic link. Please try again.")}finally{d(!1)}};return o?e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto",children:e.jsx(W,{className:"w-8 h-8 text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"text-sm text-gray-600 mt-2",children:["We've sent a magic link to ",e.jsx("strong",{children:r})]})]}),e.jsx(k,{variant:"outline",onClick:()=>h(!1),className:"w-full",children:"Back to login"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome to PromptLoop"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your account"})]}),e.jsxs("form",{onSubmit:T,className:"space-y-4",children:[e.jsx(q,{type:"email",label:"Email",placeholder:"Enter your email",value:r,onChange:y=>s(y.target.value),required:!0}),e.jsx(q,{type:"password",label:"Password (optional)",placeholder:"Enter your password",value:n,onChange:y=>l(y.target.value),helperText:"Leave blank to use magic link"}),g&&e.jsx("div",{className:"text-sm text-red-600 bg-red-50 p-3 rounded-md",children:g}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(k,{type:"submit",className:"w-full",loading:b,disabled:!r,children:[e.jsx(pe,{className:"w-4 h-4 mr-2"}),"Sign In"]}),e.jsxs(k,{type:"button",variant:"outline",className:"w-full",onClick:p,loading:b,disabled:!r,children:[e.jsx(W,{className:"w-4 h-4 mr-2"}),"Send Magic Link"]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),e.jsxs(k,{type:"button",variant:"outline",className:"w-full",onClick:i,loading:b,children:[e.jsx(oe,{className:"w-4 h-4 mr-2"}),"Google"]})]})]}),e.jsxs("div",{className:"text-center text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx("button",{onClick:t,className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign up"})]})]})},Ge=({children:t})=>{const{user:r,isLoading:s}=V(),[n,l]=m.useState("login");return s?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(G,{className:"w-8 h-8 text-white"})}),e.jsx(te,{className:"w-6 h-6 animate-spin mx-auto text-primary-600"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Loading PromptLoop..."})]})}):r?e.jsx(e.Fragment,{children:t}):e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(G,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"PromptLoop"}),e.jsx("p",{className:"text-gray-600",children:"Universal PromptOps Platform"})]}),e.jsx(E,{children:e.jsx(Ue,{onToggleMode:()=>l(n==="login"?"signup":"login")})}),e.jsx("div",{className:"text-center mt-6 text-xs text-gray-500",children:e.jsx("p",{children:"By signing in, you agree to our Terms of Service and Privacy Policy"})})]})})};function $e(){const[t,r]=m.useState("enhancer"),{user:s,signOut:n}=V();m.useEffect(()=>{const d=()=>{const g=window.location.hash.slice(1);["enhancer","library","runner","benchmark","settings"].includes(g)&&r(g)};return d(),window.addEventListener("hashchange",d),()=>window.removeEventListener("hashchange",d)},[]),m.useEffect(()=>{window.location.hash=t},[t]);const l=()=>{switch(t){case"enhancer":return e.jsx(Y,{});case"library":return e.jsx(Ae,{});case"runner":return e.jsx(Re,{});case"benchmark":return e.jsx(Ie,{});case"settings":return e.jsx(ze,{});default:return e.jsx(Y,{})}},b=()=>{r("enhancer")};return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:e.jsx(G,{className:"w-5 h-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"PromptLoop"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Universal PromptOps Platform"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(k,{onClick:b,children:[e.jsx(ye,{className:"w-4 h-4 mr-2"}),"New Prompt"]}),s&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.avatar?e.jsx("img",{src:s.avatar,alt:s.name,className:"w-8 h-8 rounded-full"}):e.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:e.jsx(Se,{className:"w-4 h-4 text-gray-600"})}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:s.name})]}),e.jsx(k,{variant:"ghost",size:"sm",onClick:n,title:"Sign out",children:e.jsx(ue,{className:"w-4 h-4"})})]})]})]})})}),e.jsxs("div",{className:"flex",children:[e.jsx(Oe,{activeTab:t,onTabChange:r}),e.jsx("main",{className:"flex-1 p-6",children:l()})]})]})})}function Fe(){return e.jsx(Be,{children:e.jsx(Ge,{children:e.jsx($e,{})})})}ie(document.getElementById("root")).render(e.jsx(m.StrictMode,{children:e.jsx(Fe,{})}));
