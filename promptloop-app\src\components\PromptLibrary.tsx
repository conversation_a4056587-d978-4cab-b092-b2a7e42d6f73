import React, { useState, useEffect } from 'react';
import { Search, BookOpen, Edit, Trash2, Copy, Folder, Calendar } from 'lucide-react';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Card, CardHeader, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Modal } from './ui/Modal';
import { Textarea } from './ui/Textarea';

interface SavedPrompt {
  id: string;
  original: string;
  enhanced: string;
  category: string;
  tags: string[];
  folder?: string;
  notes?: string;
  timestamp: Date;
  lastUsed?: Date;
}

export const PromptLibrary: React.FC = () => {
  const [prompts, setPrompts] = useState<SavedPrompt[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedFolder, setSelectedFolder] = useState<string>('all');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<SavedPrompt | null>(null);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'mostUsed'>('newest');

  // Load prompts from localStorage on component mount
  useEffect(() => {
    const loadPrompts = () => {
      try {
        const savedPrompts = JSON.parse(localStorage.getItem('promptloop_prompts') || '[]');
        const parsedPrompts = savedPrompts.map((prompt: any) => ({
          ...prompt,
          timestamp: new Date(prompt.timestamp),
          lastUsed: prompt.lastUsed ? new Date(prompt.lastUsed) : undefined
        }));
        setPrompts(parsedPrompts);
      } catch (error) {
        console.error('Error loading prompts:', error);
      }
    };

    loadPrompts();
  }, []);

  // Save prompts to localStorage whenever prompts change
  useEffect(() => {
    localStorage.setItem('promptloop_prompts', JSON.stringify(prompts));
  }, [prompts]);

  // Get unique categories and folders
  const categories = ['all', ...new Set(prompts.map(p => p.category))];
  const folders = ['all', ...new Set(prompts.map(p => p.folder).filter(Boolean))];

  // Filter and sort prompts
  const filteredPrompts = prompts
    .filter(prompt => {
      const matchesSearch = searchTerm === '' || 
        prompt.original.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.enhanced.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || prompt.category === selectedCategory;
      const matchesFolder = selectedFolder === 'all' || prompt.folder === selectedFolder;
      
      return matchesSearch && matchesCategory && matchesFolder;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return a.timestamp.getTime() - b.timestamp.getTime();
        case 'mostUsed':
          return (b.lastUsed?.getTime() || 0) - (a.lastUsed?.getTime() || 0);
        default: // newest
          return b.timestamp.getTime() - a.timestamp.getTime();
      }
    });

  const handleCopyPrompt = async (prompt: SavedPrompt) => {
    await navigator.clipboard.writeText(prompt.enhanced);
    // Update last used timestamp
    setPrompts(prev => prev.map(p => 
      p.id === prompt.id ? { ...p, lastUsed: new Date() } : p
    ));
  };

  const handleDeletePrompt = (promptId: string) => {
    setPrompts(prev => prev.filter(p => p.id !== promptId));
  };

  const handleEditPrompt = (prompt: SavedPrompt) => {
    setEditingPrompt(prompt);
    setIsModalOpen(true);
  };

  const handleSaveEdit = (updatedPrompt: SavedPrompt) => {
    setPrompts(prev => prev.map(p => 
      p.id === updatedPrompt.id ? updatedPrompt : p
    ));
    setIsModalOpen(false);
    setEditingPrompt(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prompt Library</h1>
          <p className="text-gray-600">Manage and organize your saved prompts</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">{filteredPrompts.length} prompts</Badge>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search prompts, tags, or content..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="input w-auto"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
              
              <select
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="input w-auto"
              >
                {folders.map(folder => (
                  <option key={folder} value={folder}>
                    {folder === 'all' ? 'All Folders' : folder}
                  </option>
                ))}
              </select>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="input w-auto"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="mostUsed">Most Used</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Prompts Grid */}
      {filteredPrompts.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No prompts found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategory !== 'all' || selectedFolder !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Start by creating your first prompt in the Prompt Enhancer'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPrompts.map((prompt) => (
            <Card key={prompt.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <Badge size="sm" className="mb-2">{prompt.category}</Badge>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {prompt.original}
                    </p>
                  </div>
                  <div className="flex space-x-1 ml-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopyPrompt(prompt)}
                      title="Copy prompt"
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEditPrompt(prompt)}
                      title="Edit prompt"
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleDeletePrompt(prompt.id)}
                      title="Delete prompt"
                    >
                      <Trash2 className="w-3 h-3 text-red-500" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-1">
                    {prompt.tags.map((tag) => (
                      <Badge key={tag} size="sm" variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  {prompt.folder && (
                    <div className="flex items-center text-xs text-gray-500">
                      <Folder className="w-3 h-3 mr-1" />
                      {prompt.folder}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {prompt.timestamp.toLocaleDateString()}
                    </div>
                    {prompt.lastUsed && (
                      <div>
                        Last used: {prompt.lastUsed.toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  
                  {prompt.notes && (
                    <p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                      {prompt.notes}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingPrompt(null);
        }}
        title="Edit Prompt"
        size="lg"
      >
        {editingPrompt && (
          <EditPromptForm
            prompt={editingPrompt}
            onSave={handleSaveEdit}
            onCancel={() => {
              setIsModalOpen(false);
              setEditingPrompt(null);
            }}
          />
        )}
      </Modal>
    </div>
  );
};

// Edit Prompt Form Component
interface EditPromptFormProps {
  prompt: SavedPrompt;
  onSave: (prompt: SavedPrompt) => void;
  onCancel: () => void;
}

const EditPromptForm: React.FC<EditPromptFormProps> = ({ prompt, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    category: prompt.category,
    tags: prompt.tags.join(', '),
    folder: prompt.folder || '',
    notes: prompt.notes || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...prompt,
      category: formData.category,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      folder: formData.folder || undefined,
      notes: formData.notes || undefined
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        label="Category"
        value={formData.category}
        onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
        required
      />
      
      <Input
        label="Tags"
        value={formData.tags}
        onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
        helperText="Separate tags with commas"
      />
      
      <Input
        label="Folder"
        value={formData.folder}
        onChange={(e) => setFormData(prev => ({ ...prev, folder: e.target.value }))}
      />
      
      <Textarea
        label="Notes"
        value={formData.notes}
        onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
        placeholder="Add any notes about this prompt..."
      />
      
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Changes
        </Button>
      </div>
    </form>
  );
};
