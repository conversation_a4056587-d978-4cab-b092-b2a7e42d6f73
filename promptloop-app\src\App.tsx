import { useState, useEffect } from 'react';
import { Brain, Plus, User, LogOut } from 'lucide-react';
import { PromptEnhancer } from './components/PromptEnhancer';
import { PromptLibrary } from './components/PromptLibrary';
import { PromptRunner } from './components/PromptRunner';
import { BenchmarkPlayground } from './components/BenchmarkPlayground';
import { SettingsPanel } from './components/SettingsPanel';
import { Navigation } from './components/Navigation';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { AuthGuard } from './components/auth/AuthGuard';
import { Button } from './components/ui/Button';

type TabType = 'enhancer' | 'library' | 'runner' | 'benchmark' | 'settings';

function AppContent() {
  const [activeTab, setActiveTab] = useState<TabType>('enhancer');
  const { user, signOut } = useAuth();

  // Handle URL hash navigation
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1) as TabType;
      if (['enhancer', 'library', 'runner', 'benchmark', 'settings'].includes(hash)) {
        setActiveTab(hash);
      }
    };

    handleHashChange(); // Check initial hash
    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  // Update URL hash when tab changes
  useEffect(() => {
    window.location.hash = activeTab;
  }, [activeTab]);

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'enhancer':
        return <PromptEnhancer />;
      case 'library':
        return <PromptLibrary />;
      case 'runner':
        return <PromptRunner />;
      case 'benchmark':
        return <BenchmarkPlayground />;
      case 'settings':
        return <SettingsPanel />;
      default:
        return <PromptEnhancer />;
    }
  };

  const handleNewPrompt = () => {
    setActiveTab('enhancer');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">PromptLoop</h1>
                  <p className="text-sm text-gray-500">Universal PromptOps Platform</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <Button onClick={handleNewPrompt}>
                  <Plus className="w-4 h-4 mr-2" />
                  New Prompt
                </Button>

                {user && (
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      {user.avatar ? (
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600" />
                        </div>
                      )}
                      <span className="text-sm font-medium text-gray-700">{user.name}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={signOut}
                      title="Sign out"
                    >
                      <LogOut className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        <div className="flex">
          <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
          <main className="flex-1 p-6">
            {renderActiveTab()}
          </main>
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AuthGuard>
        <AppContent />
      </AuthGuard>
    </AuthProvider>
  );
}

export default App;
