import{c,r as i,j as e,a as n,Z as r,B as l,S as m,d as x,R as o}from"./index-9328dd03.js";const d=c("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);function p(){const[s,t]=i.useState("quick"),a=()=>{chrome.tabs.create({url:chrome.runtime.getURL("index.html")})};return e.jsxs("div",{className:"w-full h-full bg-white",children:[e.jsx("header",{className:"bg-primary-600 text-white p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(n,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:"PromptLoop"})]}),e.jsx("button",{onClick:a,className:"p-1 hover:bg-primary-700 rounded",title:"Open full app",children:e.jsx(d,{className:"w-4 h-4"})})]})}),e.jsxs("nav",{className:"flex border-b border-gray-200",children:[e.jsxs("button",{onClick:()=>t("quick"),className:`flex-1 py-3 px-4 text-sm font-medium ${s==="quick"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx(r,{className:"w-4 h-4 mx-auto mb-1"}),"Quick"]}),e.jsxs("button",{onClick:()=>t("library"),className:`flex-1 py-3 px-4 text-sm font-medium ${s==="library"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx(l,{className:"w-4 h-4 mx-auto mb-1"}),"Library"]}),e.jsxs("button",{onClick:()=>t("settings"),className:`flex-1 py-3 px-4 text-sm font-medium ${s==="settings"?"text-primary-600 border-b-2 border-primary-600":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx(m,{className:"w-4 h-4 mx-auto mb-1"}),"Settings"]})]}),e.jsxs("main",{className:"p-4 h-96 overflow-y-auto",children:[s==="quick"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quick Prompt Enhancement"}),e.jsx("textarea",{className:"input w-full h-24 resize-none",placeholder:"Enter your idea... (e.g., 'write a cold email for my startup')"})]}),e.jsxs("button",{className:"btn btn-primary w-full",children:[e.jsx(r,{className:"w-4 h-4 mr-2"}),"Enhance Prompt"]})]}),s==="library"&&e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx(l,{className:"w-8 h-8 mx-auto mb-2 text-gray-400"}),e.jsx("p",{className:"text-sm",children:"No saved prompts yet"}),e.jsx("button",{onClick:a,className:"text-primary-600 text-sm hover:underline mt-2",children:"Open full library"})]})}),s==="settings"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Quick Settings"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Auto-enhance on paste"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",className:"mr-2"}),e.jsx("span",{className:"text-sm",children:"Show notifications"})]})]})]}),e.jsx("button",{onClick:a,className:"btn btn-outline w-full",children:"Open Full Settings"})]})]})]})}x.createRoot(document.getElementById("popup-root")).render(e.jsx(o.StrictMode,{children:e.jsx(p,{})}));
