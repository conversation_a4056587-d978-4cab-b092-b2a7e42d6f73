import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ExternalLink } from 'lucide-react';

function PopupApp() {
  const [activeView, setActiveView] = useState<'quick' | 'library' | 'settings'>('quick');

  const openFullApp = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('index.html') });
  };

  return (
    <div className="w-full h-full bg-white">
      <header className="bg-primary-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5" />
            <span className="font-semibold">PromptLoop</span>
          </div>
          <button
            onClick={openFullApp}
            className="p-1 hover:bg-primary-700 rounded"
            title="Open full app"
          >
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>
      </header>

      <nav className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveView('quick')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeView === 'quick'
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Zap className="w-4 h-4 mx-auto mb-1" />
          Quick
        </button>
        <button
          onClick={() => setActiveView('library')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeView === 'library'
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <BookOpen className="w-4 h-4 mx-auto mb-1" />
          Library
        </button>
        <button
          onClick={() => setActiveView('settings')}
          className={`flex-1 py-3 px-4 text-sm font-medium ${
            activeView === 'settings'
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Settings className="w-4 h-4 mx-auto mb-1" />
          Settings
        </button>
      </nav>

      <main className="p-4 h-96 overflow-y-auto">
        {activeView === 'quick' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quick Prompt Enhancement
              </label>
              <textarea
                className="input w-full h-24 resize-none"
                placeholder="Enter your idea... (e.g., 'write a cold email for my startup')"
              />
            </div>
            <button className="btn btn-primary w-full">
              <Zap className="w-4 h-4 mr-2" />
              Enhance Prompt
            </button>
          </div>
        )}

        {activeView === 'library' && (
          <div className="space-y-3">
            <div className="text-center text-gray-500 py-8">
              <BookOpen className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">No saved prompts yet</p>
              <button
                onClick={openFullApp}
                className="text-primary-600 text-sm hover:underline mt-2"
              >
                Open full library
              </button>
            </div>
          </div>
        )}

        {activeView === 'settings' && (
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Settings</h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Auto-enhance on paste</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Show notifications</span>
                </label>
              </div>
            </div>
            <button
              onClick={openFullApp}
              className="btn btn-outline w-full"
            >
              Open Full Settings
            </button>
          </div>
        )}
      </main>
    </div>
  );
}

export default PopupApp;
