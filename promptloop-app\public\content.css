/* PromptLoop Content Script Styles */

#promptloop-fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.promptloop-fab-button {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
  transition: all 0.2s ease;
  color: white;
}

.promptloop-fab-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
}

.promptloop-fab-button:active {
  transform: translateY(0);
}

#promptloop-context-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 8px 0;
  min-width: 180px;
  z-index: 10001;
  display: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.promptloop-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  font-size: 14px;
  color: #374151;
  display: flex;
  align-items: center;
}

.promptloop-menu-item:hover {
  background-color: #f3f4f6;
}

.promptloop-menu-item span {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Notifications */
.promptloop-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  z-index: 10002;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: promptloop-slide-in 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.promptloop-notification-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.promptloop-notification-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.promptloop-notification-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.promptloop-notification-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

@keyframes promptloop-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Platform-specific adjustments */

/* ChatGPT */
body[data-promptloop-platform="chatgpt"] #promptloop-fab {
  bottom: 80px; /* Avoid ChatGPT's own UI elements */
}

/* Claude */
body[data-promptloop-platform="claude"] #promptloop-fab {
  bottom: 30px;
}

/* Gemini */
body[data-promptloop-platform="gemini"] #promptloop-fab {
  bottom: 30px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  #promptloop-context-menu {
    background: #1f2937;
    border-color: #374151;
  }
  
  .promptloop-menu-item {
    color: #f3f4f6;
  }
  
  .promptloop-menu-item:hover {
    background-color: #374151;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #promptloop-fab {
    bottom: 15px;
    right: 15px;
  }
  
  .promptloop-fab-button {
    width: 48px;
    height: 48px;
  }
  
  #promptloop-context-menu {
    min-width: 160px;
  }
  
  .promptloop-notification {
    top: 15px;
    right: 15px;
    max-width: 250px;
    font-size: 13px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .promptloop-fab-button {
    background: #000;
    border: 2px solid #fff;
  }
  
  #promptloop-context-menu {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .promptloop-fab-button,
  .promptloop-menu-item,
  .promptloop-notification {
    transition: none;
  }
  
  .promptloop-fab-button:hover {
    transform: none;
  }
  
  @keyframes promptloop-slide-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
