import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Save, Wand2, <PERSON><PERSON><PERSON>, RefreshCw } from 'lucide-react';
import { Button } from './ui/Button';
import { Textarea } from './ui/Textarea';
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';

interface EnhancedPrompt {
  original: string;
  enhanced: string;
  category: string;
  tags: string[];
  timestamp: Date;
}

export const PromptEnhancer: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [enhancedPrompt, setEnhancedPrompt] = useState<EnhancedPrompt | null>(null);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const mockEnhancePrompt = async (input: string): Promise<EnhancedPrompt> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock enhancement logic based on input patterns
    let enhanced = '';
    let category = 'General';
    let tags: string[] = [];

    if (input.toLowerCase().includes('email') || input.toLowerCase().includes('cold')) {
      category = 'Email Marketing';
      tags = ['email', 'marketing', 'outreach'];
      enhanced = `Act as an expert email marketing specialist. Write a compelling cold email that:

**Context & Purpose:**
- ${input}

**Requirements:**
- Keep it under 150 words
- Include a clear value proposition
- Add a specific call-to-action
- Use a conversational, professional tone
- Personalize with recipient research

**Structure:**
1. Attention-grabbing subject line
2. Brief personal connection or compliment
3. Clear problem identification
4. Your solution/value proposition
5. Specific next step/CTA

**Output Format:**
- Subject line
- Email body
- Follow-up suggestions

Please make it authentic and avoid overly salesy language.`;
    } else if (input.toLowerCase().includes('code') || input.toLowerCase().includes('programming')) {
      category = 'Development';
      tags = ['coding', 'programming', 'development'];
      enhanced = `Act as a senior software engineer and code reviewer. Help with the following coding task:

**Task:** ${input}

**Requirements:**
- Write clean, readable, and maintainable code
- Include proper error handling
- Add meaningful comments for complex logic
- Follow best practices and conventions
- Consider performance implications

**Please provide:**
1. Complete code solution
2. Explanation of approach
3. Alternative solutions if applicable
4. Testing recommendations
5. Potential edge cases to consider

**Code Quality Standards:**
- Use descriptive variable names
- Keep functions focused and small
- Handle edge cases appropriately
- Include type hints where applicable`;
    } else if (input.toLowerCase().includes('content') || input.toLowerCase().includes('blog')) {
      category = 'Content Creation';
      tags = ['content', 'writing', 'blog'];
      enhanced = `Act as a professional content strategist and copywriter. Create engaging content for:

**Brief:** ${input}

**Content Strategy:**
- Target audience analysis
- Key messaging and tone
- SEO considerations
- Engagement optimization

**Deliverables:**
1. Compelling headline options (3-5 variations)
2. Detailed content outline
3. Key points and supporting arguments
4. Call-to-action suggestions
5. Distribution strategy recommendations

**Quality Standards:**
- Original, valuable insights
- Clear, scannable structure
- Actionable takeaways
- Optimized for target platform
- Brand voice consistency

Please ensure the content provides genuine value and stands out in a crowded content landscape.`;
    } else {
      category = 'General';
      tags = ['general', 'assistant'];
      enhanced = `Act as a knowledgeable and helpful assistant. Please help with the following request:

**Request:** ${input}

**Approach:**
- Provide comprehensive and accurate information
- Break down complex topics into digestible parts
- Include practical examples where relevant
- Offer actionable next steps
- Consider different perspectives

**Response Structure:**
1. Clear understanding of the request
2. Detailed explanation or solution
3. Supporting examples or evidence
4. Practical implementation steps
5. Additional resources or considerations

**Quality Standards:**
- Accurate and up-to-date information
- Clear and concise communication
- Practical and actionable advice
- Appropriate depth for the topic
- Professional and helpful tone

Please ensure your response is thorough yet accessible.`;
    }

    return {
      original: input,
      enhanced,
      category,
      tags,
      timestamp: new Date()
    };
  };

  const handleEnhance = async () => {
    if (!inputText.trim()) return;

    setIsEnhancing(true);
    try {
      const result = await mockEnhancePrompt(inputText);
      setEnhancedPrompt(result);
    } catch (error) {
      console.error('Enhancement failed:', error);
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleCopy = async () => {
    if (enhancedPrompt) {
      await navigator.clipboard.writeText(enhancedPrompt.enhanced);
    }
  };

  const handleSave = async () => {
    if (!enhancedPrompt) return;

    setIsSaving(true);
    try {
      // Mock save to localStorage
      const savedPrompts = JSON.parse(localStorage.getItem('promptloop_prompts') || '[]');
      savedPrompts.push({
        id: Date.now().toString(),
        ...enhancedPrompt
      });
      localStorage.setItem('promptloop_prompts', JSON.stringify(savedPrompts));
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setInputText('');
    setEnhancedPrompt(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prompt Enhancer</h1>
          <p className="text-gray-600">Transform your ideas into powerful, structured prompts</p>
        </div>
        <div className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5 text-primary-600" />
          <span className="text-sm text-gray-500">AI-Powered Enhancement</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wand2 className="w-5 h-5 mr-2 text-primary-600" />
              Your Idea
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Enter your vague idea here... 

Examples:
• 'Write a cold email for my AI startup'
• 'Help me debug this React component'
• 'Create a blog post about productivity'
• 'Explain quantum computing simply'"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              className="min-h-[200px]"
            />
            
            <div className="flex space-x-2">
              <Button
                onClick={handleEnhance}
                loading={isEnhancing}
                disabled={!inputText.trim()}
                className="flex-1"
              >
                <Zap className="w-4 h-4 mr-2" />
                {isEnhancing ? 'Enhancing...' : 'Enhance Prompt'}
              </Button>
              
              {(inputText || enhancedPrompt) && (
                <Button
                  variant="outline"
                  onClick={handleReset}
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Output Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-primary-600" />
                Enhanced Prompt
              </div>
              {enhancedPrompt && (
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">{enhancedPrompt.category}</Badge>
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCopy}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSave}
                      loading={isSaving}
                    >
                      <Save className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {enhancedPrompt ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-1">
                  {enhancedPrompt.tags.map((tag) => (
                    <Badge key={tag} size="sm" variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 max-h-[300px] overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                    {enhancedPrompt.enhanced}
                  </pre>
                </div>
                
                <div className="text-xs text-gray-500">
                  Enhanced on {enhancedPrompt.timestamp.toLocaleString()}
                </div>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Wand2 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Your enhanced prompt will appear here</p>
                <p className="text-sm mt-1">Enter an idea and click "Enhance Prompt" to get started</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
